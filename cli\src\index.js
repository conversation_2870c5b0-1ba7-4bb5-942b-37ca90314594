#!/usr/bin/env node

import { Command } from 'commander';
import { DeviceDatabase } from './lib/database.js';
import { DeviceExtractor } from './lib/deviceExtractor.js';
import { DeviceDetailsExtractor } from './lib/deviceDetailsExtractor.js';
import { RetryProcessor } from './lib/retryProcessor.js';
import { join } from 'path';
import { existsSync } from 'fs';

const program = new Command();

program
  .name('eudamed-extract')
  .description('Extract device data from EUDAMED API and save to SQLite database')
  .version('1.0.0');

// Common options for all commands
const commonOptions = [
  ['-o, --output <path>', 'Output SQLite database file path', './data/devices.db'],
  ['-l, --language <code>', 'Language code for API requests', 'en'],
  ['-t, --test', 'Show what would be extracted without actually doing it'],
  ['-s, --stats', 'Show database statistics after extraction']
];

// Page-based options for deviceSummary and deviceDetails commands
const pageOptions = [
  ['-s, --start-page <number>', 'Starting page number (0-based)', '0'],
  ['-e, --end-page <number>', 'Ending page number (0-based, inclusive)'],
  ['-r, --page-range <range>', 'Page range (e.g., "0-10,15,20-25")'],
  ['-c, --concurrency <number>', 'Concurrency limit for parallel requests', '8'],
  ['-p, --page-size <number>', 'Number of devices per page', '300']
];

// Device details specific options
const detailsOptions = [
  ['-dc, --details-concurrency <number>', 'Concurrency limit for device details requests', '3']
];

// Helper function to add options to a command
function addOptionsToCommand(command, optionSets) {
  optionSets.forEach(optionSet => {
    optionSet.forEach(option => {
      command.option(...option);
    });
  });
  return command;
}
// deviceSummary command
const deviceSummaryCommand = program
  .command('deviceSummary')
  .description('Extract device summary data (paginated device listing)');

addOptionsToCommand(deviceSummaryCommand, [commonOptions, pageOptions]);

deviceSummaryCommand.action(async (options) => {
  try {
    await executeDeviceSummary(options);
  } catch (error) {
    console.error('\n❌ Error:', error.message);
    process.exit(1);
  }
});

// deviceDetails command
const deviceDetailsCommand = program
  .command('deviceDetails')
  .description('Extract comprehensive device details (3 API calls per device)');

addOptionsToCommand(deviceDetailsCommand, [commonOptions, pageOptions, detailsOptions]);

deviceDetailsCommand.action(async (options) => {
  try {
    await executeDeviceDetails(options);
  } catch (error) {
    console.error('\n❌ Error:', error.message);
    process.exit(1);
  }
});

// retry command
const retryCommand = program
  .command('retry')
  .description('Retry failed API requests from the api_request_errors table')
  .option('-m, --max-retries <number>', 'Maximum retry attempts per request', '3')
  .option('-f, --filter-resource <type>', 'Filter by resource type (deviceSummary, deviceUDIDI, deviceBasicUDI)')
  .option('-c, --concurrency <number>', 'Concurrency limit for retry requests', '5');

addOptionsToCommand(retryCommand, [commonOptions]);

retryCommand.action(async (options) => {
  try {
    await executeRetry(options);
  } catch (error) {
    console.error('\n❌ Error:', error.message);
    process.exit(1);
  }
});

// Implementation functions
async function executeDeviceSummary(options) {
  // Parse numeric options
  const startPage = parseInt(options.startPage);
  const endPage = options.endPage ? parseInt(options.endPage) : null;
  const concurrency = parseInt(options.concurrency);
  const pageSize = parseInt(options.pageSize);

  // Validate options
  if (isNaN(startPage) || startPage < 0) {
    console.error('Error: Start page must be a non-negative number');
    process.exit(1);
  }

  if (endPage !== null && (isNaN(endPage) || endPage < startPage)) {
    console.error('Error: End page must be a number greater than or equal to start page');
    process.exit(1);
  }

  if (isNaN(concurrency) || concurrency < 1 || concurrency > 20) {
    console.error('Error: Concurrency must be between 1 and 20');
    process.exit(1);
  }

  if (isNaN(pageSize) || pageSize < 1 || pageSize > 1000) {
    console.error('Error: Page size must be between 1 and 1000');
    process.exit(1);
  }

  if (options.pageRange && (options.endPage || startPage !== 0)) {
    console.error('Error: Cannot use --page-range with --start-page or --end-page');
    process.exit(1);
  }

  console.log('🚀 EUDAMED Device Extractor - Device Summary');
  console.log('=============================================');
  console.log(`Output database: ${options.output}`);
  console.log(`Page size: ${pageSize}`);
  console.log(`Concurrency: ${concurrency}`);
  console.log(`📋 SUMMARY EXTRACTION MODE - 1 API call per page`);
  console.log(`  • Pages processed: Parallel (concurrency: ${concurrency})`);

  if (options.pageRange) {
    console.log(`Page range: ${options.pageRange}`);
  } else if (endPage !== null) {
    console.log(`Pages: ${startPage} to ${endPage}`);
  } else {
    console.log(`Starting from page: ${startPage} (all remaining pages)`);
  }

  if (options.test) {
    console.log('\n⚠️  TEST MODE - No data will be saved');
  }

  console.log('');

  // Initialize extractor for device summary
  const extractor = new DeviceExtractor({
    pageSize,
    concurrencyLimit: concurrency,
    language: options.language
  });

  // For test mode, just get total pages info
  if (options.test) {
    const { totalPages, totalElements } = await extractor.getTotalPages();

    let pagesToProcess;
    if (options.pageRange) {
      pagesToProcess = extractor.parsePageRange(options.pageRange, totalPages);
    } else if (endPage !== null) {
      const actualEndPage = Math.min(endPage, totalPages - 1);
      pagesToProcess = Array.from({ length: actualEndPage - startPage + 1 }, (_, i) => startPage + i);
    } else {
      pagesToProcess = Array.from({ length: totalPages - startPage }, (_, i) => startPage + i);
    }

    console.log(`\n📊 Test Results:`);
    console.log(`Total pages available: ${totalPages}`);
    console.log(`Total elements available: ${totalElements}`);
    console.log(`Pages that would be processed: ${pagesToProcess.length}`);
    console.log(`Estimated devices to extract: ${pagesToProcess.length * pageSize}`);
    console.log(`Processing architecture: Parallel pages`);
    console.log(`Estimated requests: ${pagesToProcess.length}`);
    console.log(`Estimated time (rough): ${Math.ceil(pagesToProcess.length / concurrency * 2)} seconds`);

    return;
  }

  // Initialize database
  const database = new DeviceDatabase(options.output);
  await database.initialize();

  // Show initial database stats
  if (existsSync(options.output)) {
    const initialCount = database.getDeviceCount();
    console.log(`📊 Initial database contains ${initialCount} devices\n`);
  }

  // Extract devices using device summary method
  const result = await extractor.extractDevices(database, {
    startPage,
    endPage,
    pageRange: options.pageRange
  });

  // Show results
  console.log('\n✅ Device Summary Extraction completed!');
  console.log('=======================================');
  console.log(`Processing time: ${(result.processingTimeMs / 1000).toFixed(2)} seconds`);
  console.log(`Total pages processed: ${result.pagesProcessed}`);
  console.log(`Successful pages: ${result.successfulPages}`);
  console.log(`Failed pages: ${result.failedPages}`);
  console.log(`Total devices saved: ${result.totalSavedRecords}`);

  // Show database stats
  if (options.stats) {
    const finalCount = database.getDeviceCount();
    console.log(`\n📊 Final database contains ${finalCount} devices`);
  }

  // Show failed pages if any
  if (result.failedPages > 0) {
    console.log('\n⚠️  Failed pages:');
    result.pageResults.forEach(page => {
      console.log(`  Page ${page.pageNumber}: ${page.error}`);
    });
  }

  // Close database
  database.close();
}

async function executeDeviceDetails(options) {
  // Parse numeric options
  const startPage = parseInt(options.startPage);
  const endPage = options.endPage ? parseInt(options.endPage) : null;
  const concurrency = parseInt(options.concurrency);
  const pageSize = parseInt(options.pageSize);
  const detailsConcurrency = parseInt(options.detailsConcurrency);

  // Validate options
  if (isNaN(startPage) || startPage < 0) {
    console.error('Error: Start page must be a non-negative number');
    process.exit(1);
  }

  if (endPage !== null && (isNaN(endPage) || endPage < startPage)) {
    console.error('Error: End page must be a number greater than or equal to start page');
    process.exit(1);
  }

  if (isNaN(concurrency) || concurrency < 1 || concurrency > 20) {
    console.error('Error: Concurrency must be between 1 and 20');
    process.exit(1);
  }

  if (isNaN(pageSize) || pageSize < 1 || pageSize > 1000) {
    console.error('Error: Page size must be between 1 and 1000');
    process.exit(1);
  }

  if (isNaN(detailsConcurrency) || detailsConcurrency < 1 || detailsConcurrency > 10) {
    console.error('Error: Details concurrency must be between 1 and 10');
    process.exit(1);
  }

  if (options.pageRange && (options.endPage || startPage !== 0)) {
    console.error('Error: Cannot use --page-range with --start-page or --end-page');
    process.exit(1);
  }

  console.log('🚀 EUDAMED Device Extractor - Device Details');
  console.log('============================================');
  console.log(`Output database: ${options.output}`);
  console.log(`Page size: ${pageSize}`);
  console.log(`Concurrency: ${concurrency}`);
  console.log(`🔍 DETAILED EXTRACTION MODE`);
  console.log(`  • Pages processed: Sequential (one at a time)`);
  console.log(`  • Device details: Parallel within each page (concurrency: ${detailsConcurrency})`);
  console.log(`  • API calls per device: 3 (search + basicUdi + details)`);

  if (options.pageRange) {
    console.log(`Page range: ${options.pageRange}`);
  } else if (endPage !== null) {
    console.log(`Pages: ${startPage} to ${endPage}`);
  } else {
    console.log(`Starting from page: ${startPage} (all remaining pages)`);
  }

  if (options.test) {
    console.log('\n⚠️  TEST MODE - No data will be saved');
  }

  console.log('');

  // Initialize extractor for device details
  const extractor = new DeviceDetailsExtractor({
    pageSize,
    concurrencyLimit: concurrency,
    detailsConcurrencyLimit: detailsConcurrency,
    language: options.language
  });

  // For test mode, just get total pages info
  if (options.test) {
    const { totalPages, totalElements } = await extractor.getTotalPages();

    let pagesToProcess;
    if (options.pageRange) {
      pagesToProcess = extractor.parsePageRange(options.pageRange, totalPages);
    } else if (endPage !== null) {
      const actualEndPage = Math.min(endPage, totalPages - 1);
      pagesToProcess = Array.from({ length: actualEndPage - startPage + 1 }, (_, i) => startPage + i);
    } else {
      pagesToProcess = Array.from({ length: totalPages - startPage }, (_, i) => startPage + i);
    }

    console.log(`\n📊 Test Results:`);
    console.log(`Total pages available: ${totalPages}`);
    console.log(`Total elements available: ${totalElements}`);
    console.log(`Pages that would be processed: ${pagesToProcess.length}`);
    console.log(`Estimated devices to extract: ${pagesToProcess.length * pageSize}`);
    console.log(`Processing architecture: Sequential pages, parallel device details`);
    console.log(`Estimated page requests: ${pagesToProcess.length} (sequential)`);
    console.log(`Estimated detail requests: ${pagesToProcess.length * pageSize * 2} (2 per device, ${detailsConcurrency} concurrent per page)`);
    console.log(`Total estimated requests: ${pagesToProcess.length + (pagesToProcess.length * pageSize * 2)}`);

    // More accurate time estimation for sequential page processing
    const avgDevicesPerPage = pageSize;
    const timePerPageSeconds = Math.ceil((avgDevicesPerPage * 2) / detailsConcurrency * 2); // 2 seconds per request estimate
    const totalTimeMinutes = Math.ceil((pagesToProcess.length * timePerPageSeconds) / 60);
    console.log(`Estimated time (rough): ${totalTimeMinutes} minutes (sequential page processing)`);

    return;
  }

  // Initialize database
  const database = new DeviceDatabase(options.output);
  await database.initialize();

  // Show initial database stats
  if (existsSync(options.output)) {
    const initialDetailsCount = database.getDeviceDetailsCount();
    console.log(`📊 Initial database contains ${initialDetailsCount} device details\n`);
  }

  // Extract device details
  const result = await extractor.extractDeviceDetails(database, {
    startPage,
    endPage,
    pageRange: options.pageRange
  });

  // Show results
  console.log('\n✅ Device Details Extraction completed!');
  console.log('=======================================');
  console.log(`Processing time: ${(result.processingTimeMs / 1000).toFixed(2)} seconds`);
  console.log(`Total pages processed: ${result.pagesProcessed}`);
  console.log(`Successful pages: ${result.successfulPages}`);
  console.log(`Failed pages: ${result.failedPages}`);
  console.log(`Total devices saved: ${result.totalSavedRecords}`);

  if (result.totalDetailsProcessed) {
    console.log(`Total device details processed: ${result.totalDetailsProcessed}`);
  }

  // Show database stats
  if (options.stats) {
    const finalDetailsCount = database.getDeviceDetailsCount();
    console.log(`\n📊 Final database contains ${finalDetailsCount} device details`);
  }

  // Show failed pages if any
  if (result.failedPages > 0) {
    console.log('\n⚠️  Failed pages:');
    result.pageResults.forEach(page => {
      console.log(`  Page ${page.pageNumber}: ${page.error}`);
    });
  }

  // Close database
  database.close();
}

async function executeRetry(options) {
  // Parse numeric options
  const maxRetries = parseInt(options.maxRetries);
  const concurrency = parseInt(options.concurrency);

  // Validate options
  if (isNaN(maxRetries) || maxRetries < 1 || maxRetries > 10) {
    console.error('Error: Max retries must be between 1 and 10');
    process.exit(1);
  }

  if (isNaN(concurrency) || concurrency < 1 || concurrency > 20) {
    console.error('Error: Concurrency must be between 1 and 20');
    process.exit(1);
  }

  console.log('🔄 EUDAMED Device Extractor - Retry Failed Requests');
  console.log('===================================================');
  console.log(`Output database: ${options.output}`);
  console.log(`Max retries per request: ${maxRetries}`);
  console.log(`Concurrency: ${concurrency}`);

  if (options.filterResource) {
    console.log(`Filter resource type: ${options.filterResource}`);
  }

  if (options.test) {
    console.log('\n⚠️  TEST MODE - No retries will be performed');
  }

  console.log('');

  // Initialize database
  const database = new DeviceDatabase(options.output);
  await database.initialize();

  // Check if database exists
  if (!existsSync(options.output)) {
    console.error('Error: Database file does not exist. Run deviceSummary or deviceDetails first.');
    process.exit(1);
  }

  // Show initial error stats
  const initialErrorCount = database.getApiRequestErrorsCount();
  console.log(`📊 Database contains ${initialErrorCount} failed requests\n`);

  if (initialErrorCount === 0) {
    console.log('✅ No failed requests found to retry.');
    database.close();
    return;
  }

  // For test mode, just show what would be retried
  if (options.test) {
    const failedRequests = database.getApiRequestErrors(options.filterResource, maxRetries);

    console.log(`📊 Test Results:`);
    console.log(`Requests that would be retried: ${failedRequests.length}`);
    console.log(`   • deviceSummary: ${failedRequests.filter(r => r.resource === 'deviceSummary').length}`);
    console.log(`   • deviceUDIDI: ${failedRequests.filter(r => r.resource === 'deviceUDIDI').length}`);
    console.log(`   • deviceBasicUDI: ${failedRequests.filter(r => r.resource === 'deviceBasicUDI').length}`);

    database.close();
    return;
  }

  // Initialize retry processor
  const retryProcessor = new RetryProcessor({
    concurrencyLimit: concurrency,
    language: options.language
  });

  // Retry failed requests
  const result = await retryProcessor.retryFailedRequests(database, {
    maxRetries,
    filterResource: options.filterResource
  });

  // Show database stats
  if (options.stats) {
    const finalErrorCount = database.getApiRequestErrorsCount();
    console.log(`\n📊 Final database contains ${finalErrorCount} failed requests`);
  }

  // Close database
  database.close();
}

// Add help examples
program.addHelpText('after', `
Commands:
  deviceSummary    Extract device summary data (paginated device listing)
  deviceDetails    Extract comprehensive device details (3 API calls per device)
  retry           Retry failed API requests from the api_request_errors table

Examples:
  Device Summary Extraction:
  $ eudamed-extract deviceSummary                                    # Extract all devices (summary mode)
  $ eudamed-extract deviceSummary --start-page 0 --end-page 10       # Extract first 11 pages
  $ eudamed-extract deviceSummary --page-range "0-5,10,15-20"        # Extract specific pages
  $ eudamed-extract deviceSummary --concurrency 5 --page-size 100    # Custom settings
  $ eudamed-extract deviceSummary --output ./my-devices.db           # Custom output file
  $ eudamed-extract deviceSummary --test                             # Preview what would be extracted
  $ eudamed-extract deviceSummary --stats                            # Show database statistics

  Device Details Extraction (3 API calls per device):
  $ eudamed-extract deviceDetails                                    # Extract comprehensive device details
  $ eudamed-extract deviceDetails --start-page 0 --end-page 2        # Details for first 3 pages
  $ eudamed-extract deviceDetails --details-concurrency 2            # Lower concurrency for details
  $ eudamed-extract deviceDetails --test                             # Preview detailed extraction

  Retry Failed Requests:
  $ eudamed-extract retry                                            # Retry all failed requests
  $ eudamed-extract retry --max-retries 5                           # Set maximum retry attempts
  $ eudamed-extract retry --filter-resource deviceSummary           # Retry only specific resource type
  $ eudamed-extract retry --concurrency 3                           # Custom retry concurrency
`);

program.parse();
