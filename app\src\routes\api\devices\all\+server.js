import { json } from '@sveltejs/kit';
import { fetchEudamedData, mapDeviceSummary } from '$lib/eudamedApi';
import initSqlJs from 'sql.js';
import cliProgress from 'cli-progress';
import { fileURLToPath } from 'url';
import { dirname, join } from 'path';
import { existsSync, mkdirSync, readFileSync, writeFileSync } from 'fs';

/**
 * Compare version numbers for conflict resolution
 * @param {string|number|null} incomingVersion - Version number from incoming record
 * @param {string|number|null} existingVersion - Version number from existing database record
 * @returns {boolean} - True if incoming version should replace existing, false if should skip
 */
function shouldUpdateRecord(incomingVersion, existingVersion) {
  // Convert to numbers for comparison, treating null/undefined as 0
  const incoming = incomingVersion ? parseInt(incomingVersion, 10) : 0;
  const existing = existingVersion ? parseInt(existingVersion, 10) : 0;

  // Handle invalid version numbers
  if (isNaN(incoming)) return false;
  if (isNaN(existing)) return true;

  // Only update if incoming version is higher than existing
  return incoming > existing;
}

// Database setup and helper functions
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const DB_DIR = join(__dirname, '../../../../data');
const DB_PATH = join(DB_DIR, 'devices.db');

async function initializeDatabase() {
  // Ensure data directory exists
  if (!existsSync(DB_DIR)) {
    mkdirSync(DB_DIR, { recursive: true });
  }

  // Initialize SQL.js
  const SQL = await initSqlJs();

  // Try to load existing database or create new one
  let db;
  if (existsSync(DB_PATH)) {
    const filebuffer = readFileSync(DB_PATH);
    db = new SQL.Database(filebuffer);
  } else {
    db = new SQL.Database();
  }

  // Create devices table with schema matching mapDeviceSummary output
  db.exec(`
    CREATE TABLE IF NOT EXISTS devices (
      uuid TEXT PRIMARY KEY,
      basicUdi TEXT,
      udiDi TEXT,
      risk TEXT,
      name TEXT,
      reference TEXT,
      status TEXT,
      versionNumber TEXT,
      manufacturer_srn TEXT,
      manufacturer_name TEXT,
      manufacturer_status TEXT,
      authorisedRepresentative_srn TEXT,
      authorisedRepresentative_name TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `);

  // Create indexes for efficient queries
  db.exec(`
    CREATE INDEX IF NOT EXISTS idx_devices_udiDi ON devices(udiDi);
    CREATE INDEX IF NOT EXISTS idx_devices_basicUdi ON devices(basicUdi);
    CREATE INDEX IF NOT EXISTS idx_devices_manufacturer_srn ON devices(manufacturer_srn);
  `);

  return db;
}

function saveDevicesToDatabase(db, devices) {
  let savedCount = 0;
  let skippedCount = 0;

  // Prepare statements for version checking and insertion
  const checkVersionSql = `SELECT versionNumber FROM devices WHERE uuid = ?`;
  const insertSql = `
    INSERT OR REPLACE INTO devices (
      uuid, basicUdi, udiDi, risk, name, reference, status, versionNumber,
      manufacturer_srn, manufacturer_name, manufacturer_status,
      authorisedRepresentative_srn, authorisedRepresentative_name, updated_at
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'))
  `;

  for (const device of devices) {
    try {
      const deviceId = device.uuid;
      if (!deviceId) {
        console.warn('Skipping device without UUID:', device.udiDi || 'unknown');
        skippedCount++;
        continue;
      }

      // Check existing version if record exists
      let shouldUpdate = true;
      const existingRecord = db.exec(checkVersionSql, [deviceId]);

      if (existingRecord.length > 0 && existingRecord[0].values.length > 0) {
        const existingVersion = existingRecord[0].values[0][0];
        shouldUpdate = shouldUpdateRecord(device.versionNumber, existingVersion);

        if (!shouldUpdate) {
          console.log(`Skipping device ${deviceId} - incoming version ${device.versionNumber || 0} <= existing version ${existingVersion || 0}`);
          skippedCount++;
          continue;
        }
      }

      // Proceed with insert/update
      db.run(insertSql, [
        device.uuid || null,
        device.basicUdi || null,
        device.udiDi || null,
        device.risk || null,
        device.name || null,
        device.reference || null,
        device.status || null,
        device.versionNumber || null,
        device.manufacturer?.srn || null,
        device.manufacturer?.name || null,
        device.manufacturer?.status || null,
        device.authorisedRepresentative?.srn || null,
        device.authorisedRepresentative?.name || null
      ]);
      savedCount++;
    } catch (error) {
      console.error('Error saving device:', device.uuid || device.udiDi, error.message);
      skippedCount++;
    }
  }

  // Save database to file after batch insert
  try {
    const data = db.export();
    writeFileSync(DB_PATH, data);
  } catch (error) {
    console.error('Error saving database to file:', error);
  }

  if (skippedCount > 0) {
    console.log(`Batch complete: ${savedCount} saved, ${skippedCount} skipped due to version conflicts`);
  }

  return savedCount;
}

// Helper function to process pages in batches with concurrency control and database saving
async function processBatch(pageNumbers, pageSize, language, batchSize = 8, db, progressBar) {
  const results = [];
  let totalSavedRecords = 0;

  for (let i = 0; i < pageNumbers.length; i += batchSize) {
    const batch = pageNumbers.slice(i, i + batchSize);

    const batchPromises = batch.map(async (pageNumber) => {
      try {
        const eudamedParams = {
          page: pageNumber.toString(),
          size: pageSize.toString(),
        };

        const { data, error } = await fetchEudamedData('/api/devices/udiDiData', eudamedParams, language);

        if (error) {
          console.error(`Error fetching page ${pageNumber}:`, error);
          return { pageNumber, error, data: null, savedCount: 0 };
        }

        // Map devices using mapDeviceSummary function
        let mappedDevices = [];
        if (data.content && Array.isArray(data.content)) {
          mappedDevices = data.content
            .filter(device => device && device.primaryDi) // Filter out devices without primaryDi
            .map(device => mapDeviceSummary(device))
            .filter(device => device !== null); // Filter out null results from mapping
        }

        return { pageNumber, error: null, data: mappedDevices, savedCount: 0 };
      } catch (error) {
        console.error(`Exception fetching page ${pageNumber}:`, error);
        return { pageNumber, error: error.message, data: null, savedCount: 0 };
      }
    });

    const batchResults = await Promise.all(batchPromises);

    // Save successful batch results to database immediately
    for (const result of batchResults) {
      if (result.data && result.data.length > 0) {
        try {
          const savedCount = saveDevicesToDatabase(db, result.data);
          result.savedCount = savedCount;
          totalSavedRecords += savedCount;
        } catch (error) {
          console.error(`Error saving batch to database for page ${result.pageNumber}:`, error);
          result.savedCount = 0;
        }
      }
    }

    results.push(...batchResults);

    // Update progress bar
    if (progressBar) {
      progressBar.update(results.length, {
        savedRecords: totalSavedRecords,
        currentBatch: Math.floor(i / batchSize) + 1,
        totalBatches: Math.ceil(pageNumbers.length / batchSize)
      });
    }
  }

  return { results, totalSavedRecords };
}

export async function GET({ url }) {
  const language = url.searchParams.get('languageIso2Code') || 'en';
  const pageSize = 300; // Use same page size as other endpoints
  const concurrencyLimit = 8; // Reasonable concurrency limit

  let db;
  let progressBar;

  try {
    // Step 1: Initialize database
    console.log('Initializing database...');
    db = await initializeDatabase();
    console.log(`Database initialized at: ${DB_PATH}`);

    // Step 2: Make initial request to determine total pages
    console.log('Making initial request to determine total pages...');
    const initialParams = {
      page: '0',
      size: pageSize.toString(),
    };

    const { data: initialData, error: initialError } = await fetchEudamedData('/api/devices/udiDiData', initialParams, language);

    if (initialError) {
      return json(initialError, { status: initialError.status });
    }

    const totalPages = initialData.totalPages || 1;
    const totalElements = initialData.totalElements || 0;

    console.log(`Total pages to fetch: ${totalPages}, Total elements: ${totalElements}`);

    // Step 3: Initialize progress bar
    progressBar = new cliProgress.SingleBar({
      format: 'Progress |{bar}| {percentage}% | {value}/{total} pages | Saved: {savedRecords} records | Batch: {currentBatch}/{totalBatches} | ETA: {eta}s',
      barCompleteChar: '\u2588',
      barIncompleteChar: '\u2591',
      hideCursor: true
    });
    progressBar.start(totalPages, 0, {
      savedRecords: 0,
      currentBatch: 0,
      totalBatches: Math.ceil(totalPages / concurrencyLimit)
    });

    // Step 4: Create array of all page numbers (0 to totalPages-1)
    const pageNumbers = Array.from({ length: totalPages }, (_, i) => i);

    // Step 5: Fetch all pages concurrently with concurrency control and database saving
    console.log(`Starting parallel fetch with concurrency limit of ${concurrencyLimit}...`);
    const startTime = Date.now();

    const { results: pageResults, totalSavedRecords } = await processBatch(
      pageNumbers,
      pageSize,
      language,
      concurrencyLimit,
      db,
      progressBar
    );

    const endTime = Date.now();
    progressBar.stop();
    console.log(`Parallel fetch completed in ${endTime - startTime}ms`);

    // Step 6: Process results and handle errors
    const errors = [];
    let successfulPages = 0;
    let totalProcessedRecords = 0;

    pageResults.forEach(result => {
      if (result.error) {
        errors.push({
          page: result.pageNumber,
          error: result.error
        });
      } else if (result.data) {
        totalProcessedRecords += result.data.length;
        successfulPages++;
      }
    });

    // Step 7: Prepare metadata-only response
    const response = {
      success: true,
      metadata: {
        totalRecordsProcessed: totalProcessedRecords,
        totalRecordsSaved: totalSavedRecords,
        pagesProcessed: totalPages,
        successfulPages,
        failedPages: errors.length,
        processingTimeMs: endTime - startTime,
        processingTimeFormatted: `${((endTime - startTime) / 1000).toFixed(2)}s`,
        recordsPerSecond: Math.round(totalProcessedRecords / ((endTime - startTime) / 1000)),
        databaseLocation: DB_PATH,
        timestamp: new Date().toISOString()
      }
    };

    // Include error details if there were failures
    if (errors.length > 0) {
      response.errors = errors;
      response.metadata.warning = `${errors.length} out of ${totalPages} pages failed to fetch. Results may be incomplete.`;
    }

    console.log(`Successfully processed ${totalProcessedRecords} records and saved ${totalSavedRecords} to database from ${successfulPages}/${totalPages} pages`);

    return json(response);

  } catch (error) {
    console.error('Error fetching all UDI-DIs:', error);

    // Cleanup: stop progress bar if it was started
    if (progressBar) {
      progressBar.stop();
    }

    // Cleanup: close database connection if it was opened
    if (db) {
      try {
        db.close();
      } catch (closeError) {
        console.error('Error closing database:', closeError);
      }
    }

    return json({
      success: false,
      timestamp: new Date().toISOString(),
      status: 500,
      error: 'Internal Server Error',
      message: 'An error occurred while fetching and processing UDI-DI data.',
      path: url.pathname,
    }, {
      status: 500
    });
  } finally {
    // Ensure database is closed
    if (db) {
      try {
        db.close();
      } catch (closeError) {
        console.error('Error closing database in finally block:', closeError);
      }
    }
  }
}
