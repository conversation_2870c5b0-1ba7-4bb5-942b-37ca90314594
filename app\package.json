{"name": "app", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./jsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./jsconfig.json --watch"}, "devDependencies": {"@sveltejs/adapter-auto": "^4.0.0", "@sveltejs/kit": "^2.16.0", "@sveltejs/vite-plugin-svelte": "^5.0.0", "node-fetch": "^3.3.2", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "swagger-ui-dist": "^5.21.0", "typescript": "^5.0.0", "vite": "^6.2.5"}, "dependencies": {"cli-progress": "^3.12.0", "sql.js": "^1.13.0"}}