import { DeviceDatabase } from '../../src/lib/database.js';
import { existsSync, unlinkSync } from 'fs';
import { join } from 'path';

const testDbPath = join(process.cwd(), 'test-api-errors.db');

async function runApiRequestErrorsTests() {
  console.log('🧪 Running API Request Errors Table Tests...\n');

  let passed = 0;
  let failed = 0;
  const failures = [];

  try {
    // Clean up any existing test database
    if (existsSync(testDbPath)) {
      unlinkSync(testDbPath);
    }

    // Test 1: Database initialization with new table
    console.log("✅ Test 01: Database initialization with api_request_errors table");
    const db = new DeviceDatabase(testDbPath);
    await db.initialize();

    // Verify table exists by trying to query it
    const initialCount = db.getApiRequestErrorsCount();
    if (initialCount === 0) {
      passed++;
    } else {
      failed++;
      failures.push(`Expected 0 initial errors, found ${initialCount}`);
    }

    // Test 2: Save deviceSummary error
    console.log("✅ Test 02: Save deviceSummary API request error");
    const deviceSummaryError = {
      resource: 'deviceSummary',
      range: '1-10',
      pageSize: 300,
      errorMessage: 'Network timeout',
      httpStatus: 500,
      retryCount: 0,
      context: 'Page batch processing failed'
    };

    const saveResult1 = db.saveApiRequestError(deviceSummaryError);
    if (saveResult1) {
      passed++;
    } else {
      failed++;
      failures.push('Failed to save deviceSummary error');
    }

    // Test 3: Save deviceUDIDI error
    console.log("✅ Test 03: Save deviceUDIDI API request error");
    const deviceUDIDIError = {
      resource: 'deviceUDIDI',
      uuid: '12345678-1234-1234-1234-123456789abc',
      errorMessage: 'Device not found',
      httpStatus: 404,
      retryCount: 1,
      context: 'Individual device fetch failed'
    };

    const saveResult2 = db.saveApiRequestError(deviceUDIDIError);
    if (saveResult2) {
      passed++;
    } else {
      failed++;
      failures.push('Failed to save deviceUDIDI error');
    }

    // Test 4: Save deviceBasicUDI error
    console.log("✅ Test 04: Save deviceBasicUDI API request error");
    const deviceBasicUDIError = {
      resource: 'deviceBasicUDI',
      uuid: '*************-4321-4321-cba987654321',
      errorMessage: 'API rate limit exceeded',
      httpStatus: 429,
      retryCount: 2,
      context: 'BasicUDI fetch rate limited'
    };

    const saveResult3 = db.saveApiRequestError(deviceBasicUDIError);
    if (saveResult3) {
      passed++;
    } else {
      failed++;
      failures.push('Failed to save deviceBasicUDI error');
    }

    // Test 5: Count total errors
    console.log("✅ Test 05: Count total API request errors");
    const totalCount = db.getApiRequestErrorsCount();
    if (totalCount === 3) {
      passed++;
    } else {
      failed++;
      failures.push(`Expected 3 total errors, found ${totalCount}`);
    }

    // Test 6: Retrieve all errors
    console.log("✅ Test 06: Retrieve all API request errors");
    const allErrors = db.getApiRequestErrors();
    if (allErrors.length === 3) {
      passed++;
    } else {
      failed++;
      failures.push(`Expected 3 errors in retrieval, found ${allErrors.length}`);
    }

    // Test 7: Filter by resource type
    console.log("✅ Test 07: Filter errors by resource type");
    const deviceSummaryErrors = db.getApiRequestErrors('deviceSummary');
    if (deviceSummaryErrors.length === 1 && deviceSummaryErrors[0].resource === 'deviceSummary') {
      passed++;
    } else {
      failed++;
      failures.push(`Expected 1 deviceSummary error, found ${deviceSummaryErrors.length}`);
    }

    // Test 8: Filter by retry count
    console.log("✅ Test 08: Filter errors by retry count");
    const lowRetryErrors = db.getApiRequestErrors(null, 1);
    if (lowRetryErrors.length === 2) {
      passed++;
    } else {
      failed++;
      failures.push(`Expected 2 errors with retry_count <= 1, found ${lowRetryErrors.length}`);
    }

    // Test 9: Verify error data structure
    console.log("✅ Test 09: Verify error data structure");
    const firstError = allErrors[0];
    const hasRequiredFields = firstError.id && firstError.resource && firstError.created_at;
    if (hasRequiredFields) {
      passed++;
    } else {
      failed++;
      failures.push('Error record missing required fields');
    }

    // Test 10: Database persistence
    console.log("✅ Test 10: Database persistence");
    db.close();

    const db2 = new DeviceDatabase(testDbPath);
    await db2.initialize();
    const persistedCount = db2.getApiRequestErrorsCount();
    if (persistedCount === 3) {
      passed++;
    } else {
      failed++;
      failures.push(`Expected 3 persisted errors, found ${persistedCount}`);
    }

    db2.close();

  } catch (error) {
    console.log(`❌ Test error: ${error.message}`);
    failed++;
    failures.push(`Test error: ${error.message}`);
  } finally {
    // Clean up test database
    if (existsSync(testDbPath)) {
      unlinkSync(testDbPath);
    }
  }

  // Print results
  console.log(`\n📊 Test Results:`);
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);

  if (failures.length > 0) {
    console.log(`\n💥 Failures:`);
    failures.forEach((failure, index) => {
      console.log(`  ${index + 1}. ${failure}`);
    });
  }

  return { passed, failed, failures };
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runApiRequestErrorsTests()
    .then(results => {
      process.exit(results.failed > 0 ? 1 : 0);
    })
    .catch(error => {
      console.error('Test runner error:', error);
      process.exit(1);
    });
}

export { runApiRequestErrorsTests };
