openapi: 3.0.3
info:
  title: Assistant API for EUDAMED Data
  description: |
    A user-friendly API interface to access parsed EUDAMED API data.
    This API simplifies the EUDAMED API contract based on the provided mapping.
  version: 1.0.0
servers:
  - url: /api # Assuming a relative path for the assistant API
    description: Assistant API Server

tags:
  - name: Actors
    description: Operations related to searching and retrieving actor data via the Assistant API.
  - name: Devices
    description: Operations related to searching and retrieving device data via the Assistant API.

paths:
  /actors:
    get:
      tags:
        - Actors
      summary: Search for Actors
      description: Retrieves a paginated list of actors matching the specified search criteria.
      operationId: searchActors
      parameters:
        - name: name
          in: query
          description: Filter by Actor Name.
          required: false
          schema:
            type: string
        - name: srn
          in: query
          description: Filter by Actor's Single Registration Number (SRN).
          required: false
          schema:
            type: string
        - name: country
          in: query
          description: Filter by Country ISO 2 Code.
          required: false
          schema:
            type: string
        - name: type
          in: query
          description: Filter by Actor Type Code.
          required: false
          schema:
            type: string
        # Add pagination parameters, mapping EUDAMED page/size
        - name: page
          in: query
          description: Page number for pagination (starts from 0).
          required: false
          schema:
            type: integer
            format: int32
            minimum: 0
            default: 0
        - name: size
          in: query
          description: Number of results per page.
          required: false
          schema:
            type: integer
            format: int32
            minimum: 1
            maximum: 300 # Updated based on user feedback
            default: 300
      responses:
        '200':
          description: Successful retrieval of actors.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ActorListResponse'
        '400':
          description: Bad Request - Invalid parameter format or value.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /actors/{srn}:
    get:
      tags:
        - Actors
      summary: Get Specific Actor Details by SRN
      description: Retrieves detailed information for a specific actor identified by its SRN.
      operationId: getActorDetailsBySrn
      parameters:
        - name: srn
          in: path
          description: The SRN of the actor.
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Successful retrieval of actor details.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Actor' # Changed from ActorDetails to Actor based on mapping
        '400':
          description: Bad Request - Invalid SRN format.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
           description: Not Found - No actor found for the provided SRN.
           content:
             application/json:
               schema:
                 $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /actors/{srn}/risks:
    get:
      tags:
        - Actors
      summary: Get Risks for a Specific Actor by SRN
      description: Retrieves a list of risks associated with a specific actor identified by its SRN.
      operationId: getActorRisksBySrn
      parameters:
        - name: srn
          in: path
          description: The SRN of the actor.
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Successful retrieval of actor risks.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/RiskListResponse'
        '400':
          description: Bad Request - Invalid SRN format.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
           description: Not Found - No actor found for the provided SRN.
           content:
             application/json:
               schema:
                 $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'


  /devices:
    get:
      tags:
        - Devices
      summary: Search for Medical Devices
      description: Retrieves a paginated list of medical devices matching the specified search criteria.
      operationId: searchDevices
      parameters:
        - name: udiDi
          in: query
          description: Search by the full UDI-DI.
          required: false
          schema:
            type: string
        - name: basicUdi
          in: query
          description: Search by the Basic UDI-DI.
          required: false
          schema:
            type: string
        - name: actorName
          in: query
          description: Filter by associated Actor Name (e.g., Manufacturer).
          required: false
          schema:
            type: string
        - name: srn
          in: query
          description: Filter by associated Actor's Single Registration Number (SRN).
          required: false
          schema:
            type: string
        - name: legislation
          in: query
          description: Filter by the applicable legislation framework.
          required: false
          schema:
            type: string
        - name: risk
          in: query
          description: Filter by the device risk class.
          required: false
          schema:
            type: string
        - name: deviceName
          in: query
          description: Filter by the Device Name.
          required: false
          schema:
            type: string
        # Add pagination parameters, mapping EUDAMED page/size
        - name: page
          in: query
          description: Page number for pagination (starts from 0).
          required: false
          schema:
            type: integer
            format: int32
            minimum: 0
            default: 0
        - name: size
          in: query
          description: Number of results per page.
          required: false
          schema:
            type: integer
            format: int32
            minimum: 1
            maximum: 300 # Updated based on user feedback
            default: 300
      responses:
        '200':
          description: Successful retrieval of device data.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeviceListResponse'
        '400':
          description: Bad Request - Invalid parameter format or value.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /devices/{udi-di}:
    get:
      tags:
        - Devices
      summary: Get Specific Medical Device Details by UDI-DI
      description: Retrieves detailed information for a specific medical device identified by its UDI-DI.
      operationId: getDeviceDetailsByUdiDi
      parameters:
        - name: udi-di
          in: path
          description: The UDI-DI of the device.
          required: true
          schema:
            type: string
      responses:
        '200':
          description: Successful retrieval of device details.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Device' # Changed from DeviceDetails to Device based on mapping
        '400':
          description: Bad Request - Invalid UDI-DI format.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
           description: Not Found - No device found for the provided UDI-DI.
           content:
             application/json:
               schema:
                 $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /certificates/{certificateNumber}:
    get:
      tags:
        - Certificates
      summary: Get Specific Certificate Details by Number
      description: Retrieves detailed information for a specific certificate identified by its number.
      operationId: getCertificateDetailsByNumber
      parameters:
        - name: certificateNumber
          in: path
          description: The number of the certificate.
          required: true
          schema:
            type: string
        - name: languageIso2Code
          in: query
          description: ISO 2 code for the language of the response. Defaults to 'en'.
          required: false
          schema:
            type: string
            default: 'en'
      responses:
        '200':
          description: Successful retrieval of certificate details.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Certificate'
        '400':
          description: Bad Request - Invalid certificate number format.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
           description: Not Found - No certificate found for the provided number.
           content:
             application/json:
               schema:
                 $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'


components:
  schemas:
    # Mapped Actor Schema
    Actor:
      description: Simplified Actor representation based on EUDAMED data.
      type: object
      properties:
        uuid:
          type: string
          format: uuid
          description: Unique identifier for the actor.
        srn:
          type: string
          description: Single Registration Number (SRN) of the actor.
        name:
          type: string
          description: Name of the actor.
        email:
          type: string
          format: email
          description: Electronic mail address.
        phone:
          type: string
          description: Telephone number.
        address:
          type: string
          description: Geographical address.
        status:
          type: string
          description: Actor status code.

    ActorListResponse:
      description: Paginated list of Actors.
      type: object
      properties:
        content:
          type: array
          items:
            $ref: '#/components/schemas/Actor'
        # Include simplified pagination details
        page:
          type: integer
          description: Current page number (0-based).
        size:
          type: integer
          description: Number of elements per page.
        totalElements:
          type: integer
          format: int64
          description: Total number of items matching the query across all pages.
        totalPages:
          type: integer
          description: Total number of pages available.

    # Mapped Device Schema
    Device:
      description: Simplified Device representation based on EUDAMED data.
      type: object
      properties:
        uuid:
          type: string
          format: uuid
          description: Unique identifier for the device (UDI-DI record).
        basicUdi:
          type: string
          description: Basic UDI-DI.
        risk:
          type: string
          description: Device risk class code.
        name:
          type: string
          description: Device trade name.
        udiDi:
          type: string
          description: Primary UDI-DI.
        reference:
          type: string
          description: Reference number (Catalogue Number).
        status:
          type: string
          description: Device status code.
        manufacturer:
          $ref: '#/components/schemas/ActorSummary'
        authorisedRepresentative:
          $ref: '#/components/schemas/ActorSummary'

    ActorSummary:
      description: Simplified Manufacturer summary.
      type: object
      properties:
        srn:
          type: string
          description: Manufacturer SRN.
        name:
          type: string
          description: Manufacturer name.
        status:
          type: string
          description: Manufacturer status code.
          nullable: true

    DeviceListResponse:
      description: Paginated list of Devices.
      type: object
      properties:
        content:
          type: array
          items:
            $ref: '#/components/schemas/Device'
        # Include simplified pagination details
        page:
          type: integer
          description: Current page number (0-based).
        size:
          type: integer
          description: Number of elements per page.
        totalElements:
          type: integer
          format: int64
          description: Total number of items matching the query across all pages.
        totalPages:
          type: integer
          description: Total number of pages available.

    Risk:
      description: Simplified Risk representation.
      type: object
      properties:
        id:
          type: string
          format: uuid
          description: Unique identifier for the risk.
        type:
          type: string
          description: Type of risk.
        description:
          type: string
          description: Description of the risk.

    RiskListResponse:
      description: List of Risks.
      type: object
      properties:
        content:
          type: array
          items:
            $ref: '#/components/schemas/Risk'

    Certificate:
      description: Simplified Certificate representation based on EUDAMED data.
      type: object
      properties:
        uuid:
          type: string
          format: uuid
          description: Unique identifier for the certificate.
        number:
          type: string
          description: Certificate number.
        type:
          type: string
          description: Certificate type code.
        issueDate:
          type: string
          format: date-time
          description: Date of issue.
        startingValidityDate:
          type: string
          format: date-time
          description: Starting date of validity.
        expiryDate:
          type: string
          format: date-time
          description: Expiry date.
        status:
          type: string
          description: Certificate status code.
        notifiedBody:
          type: string
          description: SRN of the Notified Body.
        manufacturer:
          $ref: '#/components/schemas/ActorSummary'
        authorizedRepresentative:
          type: object
          properties:
            srn:
              type: string
              description: Authorized Representative SRN.
            status:
              type: string
              description: Authorized Representative status code.
              nullable: true

    # Generic Error Response (reused from EUDAMED API contract)
    ErrorResponse:
      description: Generic error structure.
      type: object
      properties:
        timestamp:
          type: string
          format: date-time
          description: Time the error occurred.
        status:
          type: integer
          format: int32
          description: HTTP Status code repeated.
        error:
          type: string
          description: Short error description.
        message:
          type: string
          description: Detailed error message.
        path:
          type: string
          description: The API path that caused the error.