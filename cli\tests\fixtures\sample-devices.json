{"description": "Sample device data for testing purposes", "version": "1.0.0", "devices": [{"uuid": "sample-device-001", "basicUdi": "SAMPLE-UDI-001", "udiDi": "SAMPLE-DI-001", "risk": "Class I", "name": "Sample Medical Device 1", "reference": "REF-001", "status": "Active", "versionNumber": "1", "manufacturer": {"srn": "SAMPLE-MFG-001", "name": "Sample Manufacturer Ltd", "status": "Active"}, "authorisedRepresentative": {"srn": "SAMPLE-AR-001", "name": "Sample Authorized Representative"}}, {"uuid": "sample-device-002", "basicUdi": "SAMPLE-UDI-002", "udiDi": "SAMPLE-DI-002", "risk": "Class IIa", "name": "Sample Medical Device 2", "reference": "REF-002", "status": "Active", "versionNumber": "2", "manufacturer": {"srn": "SAMPLE-MFG-002", "name": "Another Sample Manufacturer", "status": "Active"}, "authorisedRepresentative": {"srn": "SAMPLE-AR-002", "name": "Another Authorized Representative"}}, {"uuid": "sample-device-003", "basicUdi": "SAMPLE-UDI-003", "udiDi": "SAMPLE-DI-003", "risk": "Class III", "name": "Sample Medical Device 3", "reference": "REF-003", "status": "Inactive", "versionNumber": "1", "manufacturer": {"srn": "SAMPLE-MFG-003", "name": "Third Sample Manufacturer", "status": "Active"}, "authorisedRepresentative": null}], "versionConflictScenarios": [{"description": "Higher version should update existing record", "existingDevice": {"uuid": "conflict-test-001", "versionNumber": "1", "name": "Original Device Name"}, "incomingDevice": {"uuid": "conflict-test-001", "versionNumber": "2", "name": "Updated Device Name"}, "expectedResult": "update", "expectedFinalName": "Updated Device Name"}, {"description": "Lower version should be skipped", "existingDevice": {"uuid": "conflict-test-002", "versionNumber": "3", "name": "Current Device Name"}, "incomingDevice": {"uuid": "conflict-test-002", "versionNumber": "2", "name": "Older Device Name"}, "expectedResult": "skip", "expectedFinalName": "Current Device Name"}, {"description": "Equal version should be skipped", "existingDevice": {"uuid": "conflict-test-003", "versionNumber": "1", "name": "Existing Device Name"}, "incomingDevice": {"uuid": "conflict-test-003", "versionNumber": "1", "name": "Same Version Device Name"}, "expectedResult": "skip", "expectedFinalName": "Existing Device Name"}]}