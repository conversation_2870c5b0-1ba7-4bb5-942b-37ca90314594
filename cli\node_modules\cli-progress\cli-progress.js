const _SingleBar = require('./lib/single-bar');
const _MultiBar = require('./lib/multi-bar');
const _Presets = require('./presets/index');
const _Formatter = require('./lib/formatter');
const _defaultFormatValue = require('./lib/format-value');
const _defaultFormatBar = require('./lib/format-bar');
const _defaultFormatTime = require('./lib/format-time');

// sub-module access
module.exports = {
    Bar: _SingleBar,
    SingleBar: _SingleBar,
    MultiBar: _MultiBar,
    Presets: _Presets,
    Format: {
        Formatter: _Formatter,
        BarFormat: _defaultFormatBar,
        ValueFormat: _defaultFormatValue,
        TimeFormat: _defaultFormatTime
    }
};