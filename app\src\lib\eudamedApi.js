import fetch from 'node-fetch';
import { json } from '@sveltejs/kit';

const EUDAMED_API_BASE_URL = 'https://ec.europa.eu/tools/eudamed';

// Helper function to filter out null, empty, or undefined values
function filterEmptyValues(obj) {
  return Object.fromEntries(
    Object.entries(obj).filter(([_, value]) => value !== null && value !== '' && value !== undefined)
  );
}

// Helper function to simplify refdata codes
function simplifyRefdata(value) {
  if (typeof value === 'string' && value.startsWith('refdata.')) {
    const parts = value.split('.');
    return parts[parts.length - 1];
  }
  return value;
}

// Recursive function to process object properties for filtering and refdata simplification
function processObject(obj) {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }

  if (Array.isArray(obj)) {
    return obj.map(item => processObject(item)).filter(item => item !== null && item !== '' && item !== undefined);
  }

  const processedObj = {};
  for (const [key, value] of Object.entries(obj)) {
    const processedValue = processObject(value);
    if (processedValue !== null && processedValue !== '' && processedValue !== undefined) {
      processedObj[key] = simplifyRefdata(processedValue);
    }
  }
  // Filter out empty objects after processing
  if (Object.keys(processedObj).length === 0) {
      return undefined;
  }
  return processedObj;
}


// Generic function to fetch data from EUDAMED API
async function fetchEudamedData(endpoint, params = {}, language = 'en') {
  const url = new URL(`${EUDAMED_API_BASE_URL}${endpoint}`);
  Object.entries(params).forEach(([key, value]) => {
    if (value !== null && value !== undefined) {
      url.searchParams.append(key, value);
    }
  });

  // Append language parameter if not already in params
  if (!params.languageIso2Code) {
      url.searchParams.append('languageIso2Code', language);
  }


  //console.log('EUDAMED API Request URL:', url.toString()); // Logging

  try {
    const response = await fetch(url.toString());
    const data = await response.json();

    if (!response.ok) {
      console.error('EUDAMED API Error Response:', data); // Log the error response
      return {
        error: {
          timestamp: new Date().toISOString(),
          status: response.status,
          error: data.error || 'EUDAMED API Error',
          message: data.message || 'An error occurred while fetching data from EUDAMED.',
          eudamedResponse: data,
          path: endpoint,
        }
      };
    }

    return {data: processObject(data)};

  } catch (error) {
    console.error(`Error fetching data from EUDAMED endpoint ${endpoint}:`, error);
    return {
       error: {
        timestamp: new Date().toISOString(),
        status: 500,
        error: 'Internal Server Error',
        message: 'An error occurred while processing your request.',
        path: endpoint,
      }
    };
  }
}

// Mapping function for Actor data (summary from search)
function mapActorSummary(eudamedActor) {
  if (!eudamedActor)
    return null;

  const actor = {
    uuid: eudamedActor.uuid,
    srn: eudamedActor.eudamedIdentifier || eudamedActor.srn,
    name: eudamedActor.name,
    email: eudamedActor.electronicMail,
    phone: eudamedActor.telephone,
    address: eudamedActor.geographicalAddress,
    status: eudamedActor.actorStatus?.code,
  };

  const processedActor = processObject(actor);
  return processedActor;
}

// Mapping function for Device data (summary from search)
function mapDeviceSummary(eudamedDevice) {
  if (!eudamedDevice)
    return null;

  const device = {
    uuid: eudamedDevice.uuid,
    basicUdi: eudamedDevice.basicUdi,
    udiDi: eudamedDevice.primaryDi,
    risk: eudamedDevice.riskClass?.code,
    name: eudamedDevice.tradeName,
    reference: eudamedDevice.reference,
    status: eudamedDevice.deviceStatusType?.code,
    versionNumber: eudamedDevice.versionNumber,
    manufacturer: {
      srn: eudamedDevice.manufacturerSrn,
      name: eudamedDevice.manufacturerName,
      status: eudamedDevice.manufacturerStatus?.code,
    },
     authorisedRepresentative: {
      srn: eudamedDevice.authorisedRepresentativeSrn,
      name: eudamedDevice.authorisedRepresentativeName,
    },
  };

  const processedDevice = processObject(device)
  return processedDevice;
}



// Mapping function for Certificate data (summary from search)
function mapCertificateSummary(eudamedCertificate) {
  if (!eudamedCertificate)
    return null;

  const certificate = {
    uuid: eudamedCertificate.uuid,
    number: eudamedCertificate.certificateNumber,
    type: eudamedCertificate.certificateType?.code,
    issueDate: eudamedCertificate.issueDate,
    startingValidityDate : eudamedCertificate.startingValidityDate,
    expiryDate: eudamedCertificate.expiryDate,
    status: eudamedCertificate.certificateStatus?.code,
    notifiedBody: eudamedCertificate.notifiedBodySrn,
    manufacturer: {
      srn: eudamedCertificate.actorSrn,
      name: eudamedCertificate.actorName,
      status: eudamedCertificate.mfStatus ? eudamedCertificate.mfStatus[eudamedCertificate.actorSrn]?.code : undefined, // Assuming actorSrn is the key in mfStatuses
    },
    authorizedRepresentative: {
      srn: eudamedCertificate.authorizedRepresentativeSrns, // This might need adjustment based on actual EUDAMED response structure
      status: eudamedCertificate.arStatuses ? eudamedCertificate.arStatuses[eudamedCertificate.authorizedRepresentativeSrns]?.code : undefined, // Assuming authorizedRepresentativeSrns is the key in arStatuses
    },
  };

  const processedCertificate = processObject(certificate);
  return processedCertificate;
}

// Placeholder mapping function for Risk data (can be expanded if needed)
function mapRisk(riskData) {
    if (!riskData) return null;
    // Currently, risk determination is done directly in the endpoint,
    // but this function can be used for further processing if necessary.
    return processObject(riskData);
}


export { fetchEudamedData, mapActorSummary, mapDeviceSummary, processObject, simplifyRefdata, mapCertificateSummary, mapRisk };