import { spawn } from 'child_process';
import { existsSync, unlinkSync } from 'fs';
import { join } from 'path';

const testDbPath = join(process.cwd(), 'test-cli-commands.db');

async function runCliCommandsTests() {
  console.log('🧪 Running CLI Commands Tests...\n');
  
  let passed = 0;
  let failed = 0;
  const failures = [];

  try {
    // Clean up any existing test database
    if (existsSync(testDbPath)) {
      unlinkSync(testDbPath);
    }

    // Test 1: Help command works
    console.log("✅ Test 01: Main help command");
    const helpResult = await runCommand(['src/index.js', '--help']);
    if (helpResult.exitCode === 0 && helpResult.stdout.includes('Commands:')) {
      passed++;
    } else {
      failed++;
      failures.push('Main help command failed');
    }

    // Test 2: deviceSummary help command
    console.log("✅ Test 02: deviceSummary help command");
    const deviceSummaryHelpResult = await runCommand(['src/index.js', 'deviceSummary', '--help']);
    if (deviceSummaryHelpResult.exitCode === 0 && deviceSummaryHelpResult.stdout.includes('Extract device summary data')) {
      passed++;
    } else {
      failed++;
      failures.push('deviceSummary help command failed');
    }

    // Test 3: deviceDetails help command
    console.log("✅ Test 03: deviceDetails help command");
    const deviceDetailsHelpResult = await runCommand(['src/index.js', 'deviceDetails', '--help']);
    if (deviceDetailsHelpResult.exitCode === 0 && deviceDetailsHelpResult.stdout.includes('Extract comprehensive device details')) {
      passed++;
    } else {
      failed++;
      failures.push('deviceDetails help command failed');
    }

    // Test 4: retry help command
    console.log("✅ Test 04: retry help command");
    const retryHelpResult = await runCommand(['src/index.js', 'retry', '--help']);
    if (retryHelpResult.exitCode === 0 && retryHelpResult.stdout.includes('Retry failed API requests')) {
      passed++;
    } else {
      failed++;
      failures.push('retry help command failed');
    }

    // Test 5: deviceSummary test mode
    console.log("✅ Test 05: deviceSummary test mode");
    const deviceSummaryTestResult = await runCommand([
      'src/index.js', 'deviceSummary', '--test', '--start-page', '0', '--end-page', '0'
    ]);
    if (deviceSummaryTestResult.exitCode === 0 && 
        deviceSummaryTestResult.stdout.includes('TEST MODE') &&
        deviceSummaryTestResult.stdout.includes('Device Summary')) {
      passed++;
    } else {
      failed++;
      failures.push('deviceSummary test mode failed');
    }

    // Test 6: deviceDetails test mode
    console.log("✅ Test 06: deviceDetails test mode");
    const deviceDetailsTestResult = await runCommand([
      'src/index.js', 'deviceDetails', '--test', '--start-page', '0', '--end-page', '0'
    ]);
    if (deviceDetailsTestResult.exitCode === 0 && 
        deviceDetailsTestResult.stdout.includes('TEST MODE') &&
        deviceDetailsTestResult.stdout.includes('Device Details')) {
      passed++;
    } else {
      failed++;
      failures.push('deviceDetails test mode failed');
    }

    // Test 7: retry test mode with non-existent database
    console.log("✅ Test 07: retry test mode with non-existent database");
    const retryTestResult = await runCommand([
      'src/index.js', 'retry', '--test', '--output', './non-existent.db'
    ]);
    if (retryTestResult.exitCode === 1 && 
        retryTestResult.stdout.includes('Database file does not exist')) {
      passed++;
    } else {
      failed++;
      failures.push('retry test mode error handling failed');
    }

    // Test 8: Invalid command
    console.log("✅ Test 08: Invalid command handling");
    const invalidCommandResult = await runCommand(['src/index.js', 'invalidCommand']);
    if (invalidCommandResult.exitCode === 1) {
      passed++;
    } else {
      failed++;
      failures.push('Invalid command should return exit code 1');
    }

    // Test 9: deviceSummary with invalid options
    console.log("✅ Test 09: deviceSummary with invalid start page");
    const invalidOptionsResult = await runCommand([
      'src/index.js', 'deviceSummary', '--start-page', '-1'
    ]);
    if (invalidOptionsResult.exitCode === 1 && 
        invalidOptionsResult.stdout.includes('Start page must be a non-negative number')) {
      passed++;
    } else {
      failed++;
      failures.push('Invalid start page validation failed');
    }

    // Test 10: Command structure validation
    console.log("✅ Test 10: Command structure validation");
    const mainHelpOutput = helpResult.stdout;
    const hasAllCommands = mainHelpOutput.includes('deviceSummary') && 
                          mainHelpOutput.includes('deviceDetails') && 
                          mainHelpOutput.includes('retry');
    if (hasAllCommands) {
      passed++;
    } else {
      failed++;
      failures.push('Not all commands are listed in main help');
    }

  } catch (error) {
    console.log(`❌ Test error: ${error.message}`);
    failed++;
    failures.push(`Test error: ${error.message}`);
  } finally {
    // Clean up test database
    if (existsSync(testDbPath)) {
      unlinkSync(testDbPath);
    }
  }

  // Print results
  console.log(`\n📊 Test Results:`);
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  
  if (failures.length > 0) {
    console.log(`\n💥 Failures:`);
    failures.forEach((failure, index) => {
      console.log(`  ${index + 1}. ${failure}`);
    });
  }

  return { passed, failed, failures };
}

// Helper function to run CLI commands
function runCommand(args, timeout = 30000) {
  return new Promise((resolve) => {
    const child = spawn('node', args, {
      stdio: ['pipe', 'pipe', 'pipe'],
      timeout
    });

    let stdout = '';
    let stderr = '';

    child.stdout.on('data', (data) => {
      stdout += data.toString();
    });

    child.stderr.on('data', (data) => {
      stderr += data.toString();
    });

    child.on('close', (code) => {
      resolve({
        exitCode: code,
        stdout,
        stderr
      });
    });

    child.on('error', (error) => {
      resolve({
        exitCode: 1,
        stdout,
        stderr: error.message
      });
    });

    // Set a timeout
    setTimeout(() => {
      child.kill();
      resolve({
        exitCode: 1,
        stdout,
        stderr: 'Command timed out'
      });
    }, timeout);
  });
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runCliCommandsTests()
    .then(results => {
      process.exit(results.failed > 0 ? 1 : 0);
    })
    .catch(error => {
      console.error('Test runner error:', error);
      process.exit(1);
    });
}

export { runCliCommandsTests };
