#!/usr/bin/env node

/**
 * Integration tests for unified devices table functionality
 *
 * This test suite verifies the unified_devices table creation, data saving,
 * version-based conflict resolution, and field completeness comparison.
 *
 * Test Coverage:
 * - Unified table creation and schema
 * - Saving device summary data to unified table
 * - Saving device details data to unified table
 * - Version-based conflict resolution
 * - Field completeness comparison for equal versions
 * - Data source tracking
 * - Backward compatibility with legacy tables
 *
 * <AUTHOR> Data Processing Team
 * @since 2024-01-XX
 */

import { DeviceDatabase } from '../../src/lib/database.js';
import { existsSync, unlinkSync } from 'fs';
import { join } from 'path';

const TEST_DB_PATH = './test-unified-devices.db';

// Sample device data for testing
const sampleDeviceSummary = {
  uuid: "test-unified-001",
  basicUdi: "B-12345678901234",
  udiDi: "12345678901234567890",
  risk: "IIa",
  name: "Test Device Summary",
  reference: "REF-001",
  status: "active",
  versionNumber: "1",
  manufacturer: {
    srn: "DE-MF-000012345",
    name: "Test Manufacturer",
    status: "active"
  },
  authorisedRepresentative: {
    srn: "DE-AR-000012345",
    name: "Test AR"
  }
};

const sampleDeviceDetails = {
  uuid: "test-unified-002",
  basicUdi: "B-98765432109876",
  primaryDi: "98765432109876543210",
  secondaryDi: "98765432109876543211",
  directMarkingDi: "98765432109876543212",
  legislation: "MDR",
  riskClass: "IIb",
  device: true,
  specialDeviceType: "implantable",
  manufacturer: {
    uuid: "mfg-uuid-001",
    srn: "DE-MF-000098765",
    name: "Test Detailed Manufacturer",
    country: "Germany",
    address: "123 Test Street",
    status: "active"
  },
  authorisedRepresentative: {
    uuid: "ar-uuid-001",
    srn: "DE-AR-000098765",
    name: "Test Detailed AR",
    country: "Germany",
    address: "456 AR Street"
  },
  deviceName: "Test Device Details",
  tradeNames: [
    { language: "en", text: "Test Trade Name EN" },
    { language: "de", text: "Test Trade Name DE" }
  ],
  reference: "REF-002",
  placedOnTheMarket: "2024-01-01",
  marketInfoLinks: [
    { country: "Germany", startDate: "2024-01-01", endDate: null }
  ],
  additionalDescriptions: [
    { language: "en", text: "Additional description in English" }
  ],
  additionalInformationUrl: "https://example.com/info",
  reprocessed: false,
  baseQuantity: 1,
  reusable: false,
  singleUse: true,
  active: true,
  implantable: true,
  sterile: true,
  versionNumber: "1",
  versionDate: "2024-01-01T12:00:00.000Z",
  versionState: "registered",
  newDevice: false
};

async function runTests() {
  console.log('🧪 Running Unified Devices Integration Tests');
  console.log('==============================================\n');

  let passed = 0;
  let failed = 0;
  const failures = [];

  // Clean up any existing test database
  if (existsSync(TEST_DB_PATH)) {
    unlinkSync(TEST_DB_PATH);
  }

  try {
    // Test 1: Database initialization with unified table
    console.log("✅ Test 01: Database initialization with unified table");
    const db = new DeviceDatabase(TEST_DB_PATH);
    await db.initialize();
    
    // Verify unified table exists
    const tables = db.db.exec("SELECT name FROM sqlite_master WHERE type='table'");
    const tableNames = tables[0]?.values?.flat() || [];
    
    if (tableNames.includes('unified_devices')) {
      passed++;
    } else {
      failed++;
      failures.push("Unified devices table not created");
    }

    // Test 2: Save device summary data to unified table
    console.log("✅ Test 02: Save device summary data to unified table");
    const summaryCount = db.saveUnifiedDevices([sampleDeviceSummary], 'summary');
    if (summaryCount === 1) {
      passed++;
    } else {
      failed++;
      failures.push("Failed to save device summary to unified table");
    }

    // Test 3: Save device details data to unified table
    console.log("✅ Test 03: Save device details data to unified table");
    const detailsCount = db.saveUnifiedDevices([sampleDeviceDetails], 'details');
    if (detailsCount === 1) {
      passed++;
    } else {
      failed++;
      failures.push("Failed to save device details to unified table");
    }

    // Test 4: Verify unified device count
    console.log("✅ Test 04: Verify unified device count");
    const totalCount = db.getUnifiedDeviceCount();
    if (totalCount === 2) {
      passed++;
    } else {
      failed++;
      failures.push(`Expected 2 unified devices, got ${totalCount}`);
    }

    // Test 5: Verify count by data source
    console.log("✅ Test 05: Verify count by data source");
    const countsBySource = db.getUnifiedDeviceCountBySource();
    if (countsBySource.summary === 1 && countsBySource.details === 1) {
      passed++;
    } else {
      failed++;
      failures.push(`Expected 1 summary and 1 details, got ${JSON.stringify(countsBySource)}`);
    }

    // Test 6: Version conflict resolution - higher version should update
    console.log("✅ Test 06: Version conflict resolution - higher version updates");
    const updatedDevice = { 
      ...sampleDeviceSummary, 
      versionNumber: '2', 
      deviceName: 'Updated Test Device',
      reference: 'REF-001-UPDATED'
    };
    const updateCount = db.saveUnifiedDevices([updatedDevice], 'summary');
    if (updateCount === 1) {
      passed++;
    } else {
      failed++;
      failures.push("Higher version update failed");
    }

    // Test 7: Version conflict resolution - lower version should skip
    console.log("✅ Test 07: Version conflict resolution - lower version skips");
    const olderDevice = { 
      ...sampleDeviceSummary, 
      versionNumber: '1', 
      deviceName: 'Older Test Device' 
    };
    const skipCount = db.saveUnifiedDevices([olderDevice], 'summary');
    if (skipCount === 0) {
      passed++;
    } else {
      failed++;
      failures.push("Lower version should have been skipped");
    }

    // Test 8: Field completeness comparison for equal versions
    console.log("✅ Test 08: Field completeness comparison for equal versions");
    const moreCompleteDevice = {
      uuid: "test-unified-003",
      basicUdi: "B-11111111111111",
      primaryDi: "11111111111111111111",
      deviceName: "Complete Device",
      reference: "REF-003",
      versionNumber: "1",
      manufacturer: {
        srn: "DE-MF-000011111",
        name: "Complete Manufacturer",
        status: "active",
        country: "Germany",
        address: "Complete Address"
      },
      authorisedRepresentative: {
        srn: "DE-AR-000011111",
        name: "Complete AR"
      },
      active: true,
      sterile: true,
      implantable: false
    };

    // Save the more complete device first
    const completeCount = db.saveUnifiedDevices([moreCompleteDevice], 'details');
    
    // Try to update with less complete device (same version)
    const lessCompleteDevice = {
      uuid: "test-unified-003",
      basicUdi: "B-11111111111111",
      primaryDi: "11111111111111111111",
      deviceName: "Less Complete Device",
      versionNumber: "1"
    };
    
    const lessCompleteCount = db.saveUnifiedDevices([lessCompleteDevice], 'summary');
    
    if (completeCount === 1 && lessCompleteCount === 0) {
      passed++;
    } else {
      failed++;
      failures.push("Field completeness comparison failed");
    }

    // Test 9: Verify backward compatibility - legacy methods still work
    console.log("✅ Test 09: Verify backward compatibility - legacy methods still work");
    const legacyDevice = {
      uuid: "test-legacy-001",
      basicUdi: "B-99999999999999",
      udiDi: "99999999999999999999",
      risk: "I",
      name: "Legacy Test Device",
      versionNumber: "1",
      manufacturer: {
        srn: "DE-MF-000099999",
        name: "Legacy Manufacturer"
      }
    };
    
    const legacyCount = db.saveDevices([legacyDevice]);
    const legacyDeviceCount = db.getDeviceCount();
    const unifiedCountAfterLegacy = db.getUnifiedDeviceCount();
    
    if (legacyCount === 1 && legacyDeviceCount === 1 && unifiedCountAfterLegacy === 4) {
      passed++;
    } else {
      failed++;
      failures.push("Backward compatibility test failed");
    }

    // Test 10: Verify data source tracking
    console.log("✅ Test 10: Verify data source tracking");
    const finalCountsBySource = db.getUnifiedDeviceCountBySource();
    const expectedSummary = 2; // original + legacy save
    const expectedDetails = 2; // original + complete device
    
    if (finalCountsBySource.summary === expectedSummary && finalCountsBySource.details === expectedDetails) {
      passed++;
    } else {
      failed++;
      failures.push(`Data source tracking failed. Expected summary: ${expectedSummary}, details: ${expectedDetails}, got: ${JSON.stringify(finalCountsBySource)}`);
    }

    // Clean up
    db.close();
    if (existsSync(TEST_DB_PATH)) {
      unlinkSync(TEST_DB_PATH);
    }

  } catch (error) {
    failed++;
    failures.push(`Unexpected error: ${error.message}`);
  }

  // Report results
  console.log('\n📊 Test Results:');
  console.log('================');
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  
  if (failures.length > 0) {
    console.log('\n❌ Failures:');
    failures.forEach((failure, index) => {
      console.log(`   ${index + 1}. ${failure}`);
    });
  }

  if (failed === 0) {
    console.log('\n🎉 All tests passed! Unified devices functionality is working correctly.');
  } else {
    console.log('\n⚠️  Some tests failed. Please review the implementation.');
    process.exit(1);
  }
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runTests().catch(console.error);
}

export { runTests };
