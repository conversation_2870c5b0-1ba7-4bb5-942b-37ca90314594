import { fetchEudamedData, mapCombinedDeviceData } from './eudamedApi.js';
import cliProgress from 'cli-progress';

class DeviceDetailsExtractor {
  constructor(options = {}) {
    this.pageSize = options.pageSize || 300;
    this.detailsConcurrencyLimit = options.detailsConcurrencyLimit || 10; // Even lower for individual device details
    this.language = options.language || 'en';
    this.primaryProgressBar = null;
    this.secondaryProgressBar = null;
    this.multiBar = null;
  }

  async getTotalPages() {
    console.log('Making initial request to determine total pages...');
    const initialParams = {
      page: '0',
      size: this.pageSize.toString(),
    };

    const { data: initialData, error: initialError } = await fetchEudamedData(
      '/api/devices/udiDiData',
      initialParams,
      this.language
    );

    if (initialError) {
      throw new Error(`Failed to get initial data: ${initialError.message}`);
    }

    if (!initialData || typeof initialData.totalPages !== 'number') {
      throw new Error('Invalid response: missing totalPages information');
    }

    const totalPages = initialData.totalPages;
    const totalElements = initialData.totalElements;

    console.log(`Total pages: ${totalPages}`);
    console.log(`Total elements: ${totalElements}`);
    console.log(`Page size: ${this.pageSize}`);

    return { totalPages, totalElements };
  }

  async fetchDeviceDetails(deviceUuid) {
    try {
      // Step 2: Call getBasicUdiDataByDeviceUuid
      const { data: basicUdiData, error: basicUdiError } = await fetchEudamedData(
        `/api/devices/basicUdiData/udiDiData/${deviceUuid}`,
        {},
        this.language
      );

      if (basicUdiError) {
        console.error(`Error fetching Basic UDI Data for UUID ${deviceUuid}:`, basicUdiError);
        return { basicUdiData: null, deviceDetailsData: null, error: basicUdiError };
      }

      // Step 3: Call getDeviceDetailsByUuid
      const { data: deviceDetailsData, error: detailsError } = await fetchEudamedData(
        `/api/devices/udiDiData/${deviceUuid}`,
        {},
        this.language
      );

      if (detailsError) {
        console.error(`Error fetching Device Details for UUID ${deviceUuid}:`, detailsError);
        // Continue with null deviceDetailsData as this endpoint might not always have data
      }

      return { basicUdiData, deviceDetailsData, error: null };
    } catch (error) {
      console.error(`Unexpected error fetching details for UUID ${deviceUuid}:`, error);
      return { basicUdiData: null, deviceDetailsData: null, error: error.message };
    }
  }

  async processDeviceDetailsInBatch(devices, pageNumber) {
    const batchSize = this.detailsConcurrencyLimit;
    const results = [];
    let processedDevices = 0;

    // Reset secondary progress bar for device processing within the page
    if (this.secondaryProgressBar) {
      this.secondaryProgressBar.setTotal(devices.length);
      this.secondaryProgressBar.update(0, {
        label: 'Devices ',
        currentPage: pageNumber + 1,
        totalPages: this.secondaryProgressBar.payload.totalPages || 1
      });
    }

    // Process devices in smaller batches for detail fetching (inner loop with concurrency)
    for (let i = 0; i < devices.length; i += batchSize) {
      const batch = devices.slice(i, i + batchSize);

      const batchPromises = batch.map(async (device, index) => {
        const { basicUdiData, deviceDetailsData, error } = await this.fetchDeviceDetails(device.uuid);

        // Update secondary progress bar for each device processed
        if (this.secondaryProgressBar) {
          this.secondaryProgressBar.update(processedDevices + index + 1, {
            label: 'Devices ',
            currentPage: pageNumber + 1,
            totalPages: this.secondaryProgressBar.payload.totalPages || 1
          });
        }

        if (error) {
          return { device, mappedDevice: null, error };
        }

        // Create search data structure to match the expected format
        const searchData = {
          content: [device]
        };

        // Map the combined data
        const mappedDevice = mapCombinedDeviceData(searchData, basicUdiData, deviceDetailsData);

        return { device, mappedDevice, error: null };
      });

      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);

      processedDevices += batchResults.length;

      // Update secondary progress bar with final batch count
      if (this.secondaryProgressBar) {
        this.secondaryProgressBar.update(processedDevices, {
          label: 'Devices ',
          currentPage: pageNumber + 1,
          totalPages: this.secondaryProgressBar.payload.totalPages || 1
        });
      }
    }

    // Complete secondary progress bar
    if (this.secondaryProgressBar) {
      this.secondaryProgressBar.update(devices.length, {
        label: 'Devices ',
        currentPage: pageNumber + 1,
        totalPages: this.secondaryProgressBar.payload.totalPages || 1
      });
    }

    return results;
  }

  async processPageSequentially(pageNumbers, database) {
    const results = [];
    let totalSavedRecords = 0;
    let totalDetailsProcessed = 0;

    // Initialize dual progress bar system
    this.multiBar = new cliProgress.MultiBar({
      clearOnComplete: false,
      hideCursor: true,
      format: '{label} |{bar}| {percentage}% | {value}/{total} | Page: {currentPage}/{totalPages}',
      barCompleteChar: '\u2588',
      barIncompleteChar: '\u2591'
    });

    // Primary progress bar for page processing
    this.primaryProgressBar = this.multiBar.create(pageNumbers.length, 0, {
      label: 'Pages   ',
      currentPage: 0,
      totalPages: pageNumbers.length
    });

    // Secondary progress bar for device processing within current page
    this.secondaryProgressBar = this.multiBar.create(100, 0, {
      label: 'Devices ',
      currentPage: 0,
      totalPages: pageNumbers.length
    });

    // Process pages sequentially (outer loop)
    for (let i = 0; i < pageNumbers.length; i++) {
      const pageNumber = pageNumbers[i];

      // Update primary progress bar
      this.primaryProgressBar.update(i, {
        label: 'Pages   ',
        currentPage: i + 1,
        totalPages: pageNumbers.length
      });

      try {
        // Step 1: Fetch page data
        const eudamedParams = {
          page: pageNumber.toString(),
          size: this.pageSize.toString(),
        };

        const { data, error } = await fetchEudamedData('/api/devices/udiDiData', eudamedParams, this.language);

        if (error) {
          results.push({ pageNumber, error, data: null, savedCount: 0, detailsProcessed: 0 });
          continue;
        }

        // Filter devices with valid UUIDs
        const devicesWithUuids = data.content ? data.content.filter(device => device && device.uuid) : [];

        if (devicesWithUuids.length === 0) {
          results.push({
            pageNumber,
            error: null,
            data: {
              totalElements: data.totalElements,
              totalPages: data.totalPages,
              size: data.size,
              number: data.number,
              deviceCount: 0,
              detailsProcessed: 0,
              detailsSuccessful: 0
            },
            savedCount: 0,
            detailsProcessed: 0
          });
          continue;
        }

        // Step 2: Process device details for all devices on this page (inner loop with concurrency)
        const deviceDetailsResults = await this.processDeviceDetailsInBatch(
          devicesWithUuids,
          pageNumber
        );

        const successfulDetails = deviceDetailsResults
          .filter(result => result.mappedDevice !== null)
          .map(result => result.mappedDevice);

        // Step 3: Save device details to database
        let savedCount = 0;
        if (successfulDetails.length > 0) {
          savedCount = database.saveDeviceDetails(successfulDetails);
        }

        // Update totals
        totalSavedRecords += savedCount;
        totalDetailsProcessed += deviceDetailsResults.length;

        results.push({
          pageNumber,
          error: null,
          data: {
            totalElements: data.totalElements,
            totalPages: data.totalPages,
            size: data.size,
            number: data.number,
            deviceCount: devicesWithUuids.length,
            detailsProcessed: deviceDetailsResults.length,
            detailsSuccessful: successfulDetails.length
          },
          savedCount,
          detailsProcessed: deviceDetailsResults.length
        });

      } catch (error) {
        results.push({ pageNumber, error: error.message, data: null, savedCount: 0, detailsProcessed: 0 });
      }

      // Update primary progress bar
      this.primaryProgressBar.update(i + 1, {
        label: 'Pages   ',
        currentPage: i + 1,
        totalPages: pageNumbers.length
      });
    }

    return { results, totalSavedRecords, totalDetailsProcessed };
  }

  async extractDeviceDetails(database, options = {}) {
    const { startPage = 0, endPage = null, pageRange = null } = options;

    try {
      // Get total pages
      const { totalPages, totalElements } = await this.getTotalPages();

      // Determine which pages to fetch
      let pageNumbers;
      if (pageRange) {
        // Parse page range (e.g., "0-10,15,20-25")
        pageNumbers = this.parsePageRange(pageRange, totalPages);
      } else if (endPage !== null) {
        // Use start and end page
        const actualEndPage = Math.min(endPage, totalPages - 1);
        pageNumbers = Array.from({ length: actualEndPage - startPage + 1 }, (_, i) => startPage + i);
      } else {
        // Use all pages from startPage to end
        pageNumbers = Array.from({ length: totalPages - startPage }, (_, i) => startPage + i);
      }

      console.log(`Processing ${pageNumbers.length} pages sequentially (double-loop architecture)...`);
      console.log(`Device details concurrency limit: ${this.detailsConcurrencyLimit}`);
      const startTime = Date.now();

      // Process all pages using the new sequential approach
      const { results: pageResults, totalSavedRecords, totalDetailsProcessed } = await this.processPageSequentially(pageNumbers, database);

      const endTime = Date.now();
      const processingTime = endTime - startTime;

      // Stop progress bars
      if (this.multiBar) {
        this.multiBar.stop();
      }

      // Calculate statistics
      const successfulPages = pageResults.filter(result => !result.error).length;
      const failedPages = pageResults.filter(result => result.error).length;

      return {
        success: true,
        timestamp: new Date().toISOString(),
        processingTimeMs: processingTime,
        totalElements,
        totalPages,
        pagesProcessed: pageResults.length,
        successfulPages,
        failedPages,
        totalSavedRecords,
        totalDetailsProcessed,
        pageResults: pageResults.filter(result => result.error) // Only return failed pages for debugging
      };

    } catch (error) {
      // Stop progress bars on error
      if (this.multiBar) {
        this.multiBar.stop();
      }
      throw error;
    }
  }

  parsePageRange(rangeStr, totalPages) {
    const pages = new Set();
    const parts = rangeStr.split(',');

    for (const part of parts) {
      const trimmed = part.trim();
      if (trimmed.includes('-')) {
        // Range like "0-10"
        const [start, end] = trimmed.split('-').map(n => parseInt(n.trim()));
        if (isNaN(start) || isNaN(end)) {
          throw new Error(`Invalid page range: ${trimmed}`);
        }
        for (let i = start; i <= Math.min(end, totalPages - 1); i++) {
          pages.add(i);
        }
      } else {
        // Single page
        const page = parseInt(trimmed);
        if (isNaN(page)) {
          throw new Error(`Invalid page number: ${trimmed}`);
        }
        if (page < totalPages) {
          pages.add(page);
        }
      }
    }

    return Array.from(pages).sort((a, b) => a - b);
  }
}

export { DeviceDetailsExtractor };
