openapi: 3.0.3
info:
  title: EUDAMED Public Device Search API (Reverse-Engineered)
  description: |
    An *unofficial* OpenAPI specification based on observed network traffic
    for the EUDAMED public device search API endpoint (`/api/devices/udiDiData`).

    This contract is derived from sample URLs and partial documentation.
    **The response schemas need to be defined based on actual API responses.**
  version: 1.0.0
servers:
  - url: https://ec.europa.eu/tools/eudamed # Base URL observed from sample
    description: EUDAMED API Server

tags:
  - name: Medical Devices
    description: Operations related to searching and retrieving medical device data.
  - name: Actors
    description: Operations related to searching and retrieving actor data.

paths:
  /api/eos:
    get:
      tags:
        - Actors
      summary: Search for Actors
      description: |
        Retrieves a paginated list of actors matching the specified search criteria.
        Filters can be combined.
      operationId: searchActors
      parameters:
        # --- Pagination ---
        - name: page
          in: query
          description: Page number for pagination (starts from 0).
          required: false
          schema:
            type: integer
            format: int32
            minimum: 0
            default: 0
          example: 0
        - name: size
          in: query
          description: Number of results per page.
          required: false
          schema:
            type: integer
            format: int32
            minimum: 1
            maximum: 50
            default: 25
          example: 25
        - name: pageSize
          in: query
          description: Alias for size parameter.
          required: false
          schema:
            type: integer
            format: int32
            minimum: 1
            maximum: 50
            default: 25
          example: 25
        - name: rnd
          in: query
          description: Random number to prevent caching (timestamp).
          required: false
          schema:
            type: integer
            format: int64
          example: 1743752364426
        - name: sort
          in: query
          description: Sorting criteria in the format 'property,direction'. Multiple sort parameters are supported.
          required: false
          schema:
            type: array
            items:
              type: string
          example: ["srn,ASC", "versionNumber,DESC"]
          style: form
          explode: true

        # --- Actor Filters ---
        - name: name
          in: query
          description: Filter by Actor Name.
          required: false
          schema:
            type: string
          example: "param-name"
        - name: srn
          in: query
          description: Filter by Actor's Single Registration Number (SRN).
          required: false
          schema:
            type: string
            pattern: '^([A-Z]{2})-([A-Z]{2})-([0-9]{9})$'
          example: "param-srn"
        - name: actorTypeCode
          in: query
          description: Filter by Actor Type Code.
          required: false
          schema:
            type: string
            enum:
              - refdata.actor-type.authorised-representative
              - refdata.actor-type.importer
              - refdata.actor-type.manufacturer
              - refdata.actor-type.system-procedure-pack-producer
          example: "refdata.actor-type.manufacturer"
        - name: arImporterName
          in: query
          description: Filter by Authorised Representative or Importer Name (only applicable when actorTypeCode is manufacturer).
          required: false
          schema:
            type: string
          example: "param-ar-importer-name"
        - name: arImporterSrn
          in: query
          description: Filter by Authorised Representative or Importer SRN (only applicable when actorTypeCode is manufacturer).
          required: false
          schema:
            type: string
            pattern: '^([A-Z]{2})-([A-Z]{2})-([0-9]{9})$'
          example: "param-ar-importer-srn"
        - name: countryIso2Code
          in: query
          description: Filter by Country ISO 2 Code.
          required: false
          schema:
            type: string
            pattern: '^[A-Z]{2}$'
          example: "BE"
        - name: validatorUuid
          in: query
          description: Validator UUID (purpose unclear).
          required: false
          schema:
            type: string
            format: uuid
          example: "02210dfd-8057-4c59-88bf-c753e82d5780"
        - name: languageIso2Code
          in: query
          description: ISO 639-1 code for the language of the results.
          required: false
          schema:
            type: string
            pattern: '^[a-z]{2}$'
            default: "en"
          example: "en"
      responses:
        '200':
          description: Successful retrieval of actors.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ActorSearchResponse'
        '400':
          description: Bad Request - Invalid parameter format or value.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /api/actors/{uuid}/publicInformation:
    get:
      tags:
        - Actors
      summary: Get Specific Actor Details
      description: |
        Retrieves detailed information for a specific actor identified by its UUID.
      operationId: getActorDetailsByUuid
      parameters:
        - name: uuid
          in: path
          description: The UUID of the actor record (obtained from search results).
          required: true
          schema:
            type: string
            format: uuid
          example: "ca120f4f-ef0b-4a6d-a0b0-9ccec44ccb57"
        - name: languageIso2Code
          in: query
          description: ISO 639-1 code for the language of the results.
          required: false
          schema:
            type: string
            pattern: '^[a-z]{2}$'
            default: "en"
          example: "en"
      responses:
        '200':
          description: Successful retrieval of actor details.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ActorDetailResponse'
        '400':
          description: Bad Request - Invalid UUID format or invalid language code.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found - No actor found for the provided UUID.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /api/devices/udiDiData:
    get:
      tags:
        - Medical Devices
      summary: Search for Medical Devices (UDI DI Data)
      description: |
        Retrieves a paginated list of medical devices matching the specified search criteria.
        Filters can be combined.
      operationId: searchDevicesUdiDiData
      parameters:
        # --- Pagination ---
        - name: page
          in: query
          description: Page number for pagination (starts from 0).
          required: false
          schema:
            type: integer
            format: int32
            minimum: 0
            default: 0
          example: 0
        - name: size # Note: Sample URL also had 'pageSize'. Assuming 'size' is preferred or they are aliases. Verify.
          in: query
          description: Number of results per page.
          required: false
          schema:
            type: integer
            format: int32
            minimum: 1
            maximum: 50 # Assuming a reasonable max, verify if possible
            default: 25
          example: 25

        # --- Language ---
        - name: languageIso2Code # Preferred over potentially redundant 'iso2Code' from sample? Verify.
          in: query
          description: ISO 639-1 code for the language of the results (e.g., 'en', 'fr', 'de').
          required: false
          schema:
            type: string
            pattern: '^[a-z]{2}$' # Basic pattern for 2-letter codes
          example: "en"
        - name: iso2Code # Observed in sample, potentially redundant with languageIso2Code. Verify purpose.
          in: query
          description: ISO language code (purpose relative to languageIso2Code needs clarification).
          required: false
          schema:
            type: string
            pattern: '^[a-z]{2}$'
          example: "en"

        # --- Device Identifiers ---
        - name: primaryDi
          in: query
          description: Search by the full UDI-DI.
          required: false
          schema:
            type: string
          example: "(01)09501234567895(11)251231(10)L0T456" # Example format
        - name: basicUdi
          in: query
          description: Search by the Basic UDI-DI.
          required: false
          schema:
            type: string
          example: "EXAMPLE_BASIC_UDI_DI++" # Example format

        # --- Actor Filters ---
        - name: name # Assuming this filters by Actor Name associated with the device. Verify.
          in: query
          description: Filter by Actor Name (e.g., Manufacturer).
          required: false
          schema:
            type: string
          example: "Example Manufacturer Name"
        - name: srn
          in: query
          description: Filter by Actor's Single Registration Number (SRN).
          required: false
          schema:
            type: string
            pattern: '^([A-Z]{2})-([A-Z]{2})-(\d{9})$' # Format constraint
          example: "BE-MF-000000001"

        # --- Device Attributes & Filters ---
        - name: applicableLegislation
          in: query
          description: Filter by the applicable legislation framework.
          required: false
          schema:
            type: string
            enum:
              - refdata.applicable-legislation.aimdd
              - refdata.applicable-legislation.mdd
              - refdata.applicable-legislation.mdr
              - refdata.applicable-legislation.ivdd
              - refdata.applicable-legislation.ivdr
        - name: reference # Assuming Catalogue Number based on example value 'param-catalog-nb'. Verify.
          in: query
          description: Filter by Reference / Catalogue Number (?).
          required: false
          schema:
            type: string
          example: "CAT-123-XYZ"
        - name: deviceModel
          in: query
          description: Filter by the Device Model name/number.
          required: false
          schema:
            type: string
          example: "Model-X Rev. 2"
        - name: tradeName
          in: query
          description: Filter by the Device Trade Name.
          required: false
          schema:
            type: string
          example: "SuperStent"
        - name: deviceTypes
          in: query
          description: |
            Filter by one or more device types (comma-separated list).
            Possible values include:
            `refdata.device-type.active-device`,
            `refdata.device-type.contains-cmr-substances`,
            `refdata.device-type.containing-latex`,
            `refdata.device-type.contains-substances-considered-medicinal-product-derived`,
            `refdata.device-type.contains-substances-considered-medicinal-product`,
            `refdata.device-type.contains-endocrine-disrupting-substances`,
            `refdata.device-type.II_b_implantable_exceptions`,
            `refdata.device-type.implantable`,
            `refdata.device-type.intended-to-administer`,
            `refdata.special-mdr-device-type.made-to-order`,
            `refdata.device-type.measuring-function`,
            `refdata.device-type.needs-sterilisation`,
            `refdata.special-mdr-device-type.orthopedic`,
            `refdata.multi-component.procedure-pack`,
            `refdata.special-mdr-device-type.ready-made-spectacles`,
            `refdata.device-type.reprocessed`,
            `refdata.device-type.reusable-surgical-instrument`,
            `refdata.special-mdr-device-type.rigid-gas-permeable`,
            `refdata.special-mdr-device-type.software`,
            `refdata.special-mdr-device-type.spectacles-frames`,
            `refdata.special-mdr-device-type.spectacles-lenses`,
            `refdata.special-mdr-device-type.standard-soft-contact-lenses`,
            `refdata.device-type.sterile`,
            `refdata.multi-component.system`,
            `refdata.device-type.tissues-or-cells-from-animal-origin`,
            `refdata.device-type.tissues-or-cells-from-human-origin`
          required: false
          schema:
            type: string # Comma-separated list
          example: "refdata.device-type.implantable,refdata.device-type.sterile"
        - name: riskClassCode
          in: query
          description: Filter by the device risk class.
          required: false
          schema:
            type: string
            enum:
              - refdata.risk-class.class-i
              - refdata.risk-class.class-iia
              - refdata.risk-class.class-iib
              - refdata.risk-class.class-iii
              - refdata.risk-class.aimdd
              - refdata.risk-class.class-a
              - refdata.risk-class.class-b
              - refdata.risk-class.class-c
              - refdata.risk-class.class-d
              - refdata.risk-class.ivd-annex-ii-list-a
              - refdata.risk-class.ivd-annex-ii-list-b
              - refdata.risk-class.ivd-devices-self-testing
              - refdata.risk-class.ivd-general
        - name: deviceScopes
          in: query
          description: |
            Filter by one or more device scopes (comma-separated list).
            Possible values include:
            `refdata.device-scope.device`,
            `refdata.device-scope.system`,
            `refdata.device-scope.procedure-pack`
          required: false
          schema:
            type: string # Comma-separated list
          example: "refdata.device-scope.device"
        - name: includeHistoricalVersion
          in: query
          description: Whether to include historical (older) versions of devices in the results.
          required: false
          schema:
            type: boolean
            default: false
          example: true
        - name: medicalPurposeProcedurePack # Assuming purpose for System/Procedure Pack. Verify.
          in: query
          description: Filter by the medical purpose of a System or Procedure Pack.
          required: false
          schema:
            type: string
          example: "Surgical Kit A"
        - name: cndCode # Nomenclature Code. Verify exact meaning/system.
          in: query
          description: Filter by CND (Classificazione Nazionale Dispositivi medici) code (?).
          required: false
          schema:
            type: string
          example: "A010101"
        - name: deviceStatusCode
          in: query
          description: Filter by the status of the device model.
          required: false
          schema:
            type: string
            enum:
              - refdata.device-model-status.on-the-market
              - refdata.device-model-status.no-longer-on-the-market
              - refdata.device-model-status.not-intended-for-eu-market
          example: "refdata.device-model-status.on-the-market"

        # --- Sorting ---
        - name: sort
          in: query
          description: |
            Sorting criteria for the results. Format: `fieldName,direction`.
            Possible field names: `primaryDI`, `basicUdi`, `tradeName`, `versionNumber`.
            Possible directions: `ASC`, `DESC`.
            Possible to combine multiple sort criteria for different fields by using the sort paramter multiple times.
          required: false
          schema:
            type: string
            # Enum not practical here due to combinations, pattern is complex.
            # List examples and rely on description.
          example: "tradeName,ASC"

      responses:
        '200':
          description: Successful retrieval of device data.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeviceSearchResults' # Placeholder
              example: # Actual structure based on provided sample
                {
                  "content": [
                    {
                      "basicUdi": "MFR_CODE+DEVICE_GROUP_ID++",
                      "primaryDi": "(01)01234567890128",
                      "uuid": "a1b2c3d4-e5f6-7890-1234-567890abcdef",
                      "ulid": "01H7Z5X2Y3W4V5T6R7Q8P9N0M1",
                      "basicUdiDiDataUlid": "01H7Z5X2Y3W4V5T6R7Q8P9N0M2",
                      "riskClass": { "code": "refdata.risk-class.class-iib" },
                      "tradeName": "Example Device Trade Name",
                      "manufacturerName": "Example Manufacturer Corp.",
                      "manufacturerSrn": "DE-MF-000000XYZ",
                      "deviceStatusType": { "code": "refdata.device-model-status.on-the-market" },
                      "manufacturerNames": ["Example Manufacturer Corp."],
                      "manufacturerStatus": { "code": "refdata.actor-status.active" },
                      "latestVersion": true,
                      "versionNumber": 1,
                      "basicUdiDataUuid": "b2c3d4e5-f6a7-8901-2345-67890abcdef0",
                      "basicUdiDataUlid": "01H7Z5X2Y3W4V5T6R7Q8P9N0M3",
                      "basicUdiDataVersionState": "Published",
                      "versionState": "Current",
                      "deviceName": "Example Device Name",
                      "deviceModel": "Model X1",
                      "lastUpdateDate": "2024-03-15T10:30:00Z",
                      "reference": "REF-12345",
                      "basicUdiDataVersionNumber": 1,
                      "issuingAgency": "GS1",
                      "containerPackageCount": 1,
                      "mfOrPrSrn": "DE-MF-000000XYZ",
                      "applicableLegislation": "refdata.applicable-legislation.mdr",
                      "authorisedRepresentativeSrn": null,
                      "authorisedRepresentativeName": null,
                      "sterile": true,
                      "multiComponent": false,
                      "deviceCriterion": "Standard"
                    }
                  ],
                  "first": true,
                  "last": true,
                  "number": 0,
                  "numberOfElements": 1,
                  "size": 25,
                  "sort": [
                    {
                      "ascending": true,
                      "descending": false,
                      "direction": "ASC",
                      "ignoreCase": false,
                      "nullHandling": "NATIVE",
                      "property": "tradeName"
                    }
                  ],
                  "totalElements": 1,
                  "totalPages": 1
                }
        '400':
          description: Bad Request - Invalid parameter format, invalid enum value, etc.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse' # Placeholder
        '500':
          description: Internal Server Error - An error occurred on the EUDAMED server.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse' # Placeholder

  /api/devices/udiDiData/{uuid}:
    get:
      tags:
        - Medical Devices
      summary: Get Specific Medical Device Details (UDI DI Data)
      description: |
        Retrieves detailed information for a specific medical device identified by its UUID.
      operationId: getDeviceDetailsByUuid
      parameters:
        - name: uuid
          in: path
          description: The UUID of the device record (obtained from search results).
          required: true
          schema:
            type: string
            format: uuid
          example: "20066bf8-b042-4a36-9c89-8af09701604d"
        - name: languageIso2Code # Consistent with search endpoint
          in: query
          description: ISO 639-1 code for the language of the results (e.g., 'en', 'fr', 'de'). Defaults to 'en' if not provided or invalid.
          required: false # Assuming default behavior based on observation
          schema:
            type: string
            pattern: '^[a-z]{2}$' # Basic pattern for 2-letter codes
            default: "en"
          example: "en"
      responses:
        '200':
          description: Successful retrieval of detailed device data.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeviceDetails' # Reference to the detailed schema
              example: # Provide a snippet or reference the full sample if too large
                uuid: "20066bf8-b042-4a36-9c89-8af09701604d"
                ulid: "01JQEQ1CA1AYH49BC0YQZQN0S4"
                primaryDi:
                  code: "08435566632907"
                tradeName:
                  texts:
                    - language: { isoCode: "en" }
                      text: "Screw M1.8"
                # ... other fields based on sample_detailed_responsed.json
        '400':
          description: Bad Request - Invalid UUID format or invalid language code.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
           description: Not Found - No device found for the provided UUID.
           content:
             application/json:
               schema:
                 $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'

  /api/devices/basicUdiData/udiDiData/{uuid}:
    get:
      tags:
        - Medical Devices
      summary: Get Basic UDI Data associated with a Device UUID
      description: |
        Retrieves the Basic UDI information linked to a specific medical device identified by its UUID.
        This seems to fetch the parent Basic UDI record associated with a specific UDI-DI record's UUID.
      operationId: getBasicUdiDataByDeviceUuid
      parameters:
        - name: uuid
          in: path
          description: The UUID of the specific device record (UDI DI Data UUID).
          required: true
          schema:
            type: string
            format: uuid
          example: "4c85cb89-88bb-4019-81ee-1215363c9e9a" # Example from user request
        - name: languageIso2Code # Consistent with other endpoints
          in: query
          description: ISO 639-1 code for the language of the results (e.g., 'en', 'fr', 'de'). Defaults to 'en' if not provided or invalid.
          required: false # Assuming default behavior
          schema:
            type: string
            pattern: '^[a-z]{2}$'
            default: "en"
          example: "en"
      responses:
        '200':
          description: Successful retrieval of Basic UDI data.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/BasicUdiData' # Reference to the Basic UDI schema
              example: # Snippet based on samples/d-basic.json
                uuid: "a60245a2-980e-45c8-8165-3f48b5276a80"
                ulid: "01JJP0GNXKZDE8BZDA3447JASS"
                manufacturer:
                  name: "Hain Lifescience GmbH"
                  srn: "DE-MF-000008606"
                basicUdi:
                  code: "40490550300001100TU"
                  issuingAgency: { code: "refdata.issuing-agency.gs1" }
                deviceName: "GENERIC HBV VIRAL LOAD VER 2.0"
                riskClass: { code: "refdata.risk-class.class-d" }
                legislation: { code: "refdata.applicable-legislation.ivdr" }
                # ... other fields based on sample
        '400':
          description: Bad Request - Invalid UUID format or invalid language code.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '404':
          description: Not Found - No Basic UDI data found for the provided device UUID, or the device UUID itself doesn't exist.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'
        '500':
          description: Internal Server Error - An error occurred on the EUDAMED server.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ErrorResponse'


components:
  schemas:
    # --- Data Schemas - REFINED FROM SAMPLES ---
    DeviceSummary:
        description: Representation of a single device in search results. Based on global-search.json.
        type: object
        properties:
          basicUdi: { type: string, description: "Basic UDI-DI.", nullable: true }
          primaryDi: { type: string, description: "Primary UDI-DI.", nullable: true }
          uuid: { type: string, format: uuid, description: "Unique identifier for the UDI-DI record.", nullable: true }
          ulid: { type: string, description: "ULID identifier for the UDI-DI record.", nullable: true }
          basicUdiDiDataUlid: { type: string, description: "ULID of the associated Basic UDI record.", nullable: true }
          riskClass: { $ref: '#/components/schemas/RefDataCode', description: "Device risk class.", nullable: true }
          tradeName: { type: string, description: "Device trade name.", nullable: true }
          manufacturerName: { type: string, description: "Manufacturer name.", nullable: true }
          manufacturerSrn: { type: string, description: "Manufacturer SRN.", nullable: true }
          deviceStatusType: { $ref: '#/components/schemas/RefDataCode', description: "Device status type.", nullable: true }
          manufacturerNames: { type: array, items: { type: string }, nullable: true, description: "List of manufacturer names (often just one)." }
          manufacturerStatus: { $ref: '#/components/schemas/RefDataCode', description: "Manufacturer status.", nullable: true }
          latestVersion: { type: boolean, description: "Indicates if it's the latest version of the UDI-DI record.", nullable: true }
          versionNumber: { type: integer, format: int32, description: "Version number of the UDI-DI record.", nullable: true }
          basicUdiDataUuid: { type: string, format: uuid, description: "UUID of the associated Basic UDI record.", nullable: true }
          basicUdiDataUlid: { type: string, description: "ULID of the associated Basic UDI record.", nullable: true } # Duplicate of basicUdiDiDataUlid? Keep both as seen in sample.
          basicUdiDataVersionState: { type: string, nullable: true, description: "Version State of the associated Basic UDI record." } # Maybe VersionState schema?
          versionState: { type: string, nullable: true, description: "Version State of the UDI-DI record." } # Maybe VersionState schema?
          deviceName: { type: string, nullable: true, description: "Device name (often from Basic UDI)." }
          deviceModel: { type: string, nullable: true, description: "Device model (often from Basic UDI)." }
          lastUpdateDate: { type: string, format: date-time, nullable: true, description: "Last update date of the UDI-DI record." }
          reference: { type: string, nullable: true, description: "Reference number (Catalogue Number?)." }
          basicUdiDataVersionNumber: { type: integer, format: int32, nullable: true, description: "Version Number of the associated Basic UDI record." }
          issuingAgency: { type: string, nullable: true, description: "Issuing agency for the Primary DI. Could be RefDataCode?" }
          containerPackageCount: { type: integer, format: int32, nullable: true, default: 0, description: "Container package count." }
          mfOrPrSrn: { type: string, nullable: true, description: "Fallback of Manufacturer or SPPP SRN." }
          applicableLegislation: { type: string, nullable: true, description: "Applicable legislation (often from Basic UDI). Could be RefDataCode?" }
          authorisedRepresentativeSrn: { type: string, nullable: true, description: "Authorised representative SRN (often from Basic UDI)." }
          authorisedRepresentativeName: { type: string, nullable: true, description: "Authorised representative name (often from Basic UDI)." }
          sterile: { type: boolean, nullable: true, description: "Is device sterile?" }
          multiComponent: { type: boolean, nullable: true, description: "Is device multi-component? (Simplified for summary)" }
          deviceCriterion: { type: string, nullable: true, description: "Device criterion (e.g., Standard, Legacy)." }

    PaginationInfo: # Keep existing, looks okay.
       description: Structure containing pagination details (extracted from top-level response).
       type: object
       properties:
         first: { type: boolean, description: "Indicates if it is the first page." }
         last: { type: boolean, description: "Indicates if it is the last page." }
         number: { type: integer, format: int32, description: "Current page number (0-based)." }
         numberOfElements: { type: integer, format: int32, description: "Number of elements on the current page." }
         size: { type: integer, format: int32, description: "Page size requested." }
         sort: { type: array, description: "Sorting criteria applied to the results.", items: { $ref: '#/components/schemas/SortInfo' } }
         totalElements: { type: integer, format: int64, description: "Total number of items matching the query across all pages." }
         totalPages: { type: integer, format: int32, description: "Total number of pages available." }

    DeviceSearchResults: # Keep existing, DeviceSummary is refined.
      description: Top-level response structure for device search results, based on sample.
      type: object
      properties:
        content: { type: array, description: "The list of devices found for the current page.", items: { $ref: '#/components/schemas/DeviceSummary' } }
        first: { type: boolean, description: "Indicates if it is the first page." }
        last: { type: boolean, description: "Indicates if it is the last page." }
        number: { type: integer, format: int32, description: "Current page number (0-based)." }
        numberOfElements: { type: integer, format: int32, description: "Number of elements on the current page." }
        size: { type: integer, format: int32, description: "Page size requested." }
        sort: { type: array, description: "Sorting criteria applied to the results.", items: { $ref: '#/components/schemas/SortInfo' } }
        totalElements: { type: integer, format: int64, description: "Total number of elements." }
        totalPages: { type: integer, format: int32, description: "Total number of pages." }

    SortInfo: # Extracted from DeviceSearchResults/PaginationInfo for reusability
      description: Represents one sorting criterion.
      type: object
      properties:
        ascending: { type: boolean, nullable: true }
        descending: { type: boolean, nullable: true }
        direction: { type: string, enum: [ASC, DESC], nullable: true }
        ignoreCase: { type: boolean, nullable: true }
        nullHandling: { type: string, enum: [NATIVE, NULLS_FIRST, NULLS_LAST], nullable: true }
        property: { type: string, nullable: true }

    ErrorResponse: # Keep existing, looks okay.
      description: Generic error structure (Placeholder - Verify actual error format).
      type: object
      properties:
        timestamp: { type: string, format: date-time, description: "Time the error occurred." }
        status: { type: integer, format: int32, description: "HTTP Status code repeated.", example: 400 }
        error: { type: string, description: "Short error description (e.g., 'Bad Request').", example: "Bad Request" }
        message: { type: string, description: "Detailed error message.", example: "Invalid risk class code provided." }
        path: { type: string, description: "The API path that caused the error.", example: "/api/devices/udiDiData" }

    # --- Detailed Schemas ---

    BasicUdiData:
      description: Represents the Basic UDI Data associated with a device or group of devices. Based on samples ending in '-basic.json'. All properties are nullable as per user request.
      type: object
      properties:
        uuid: { type: string, format: uuid, nullable: true }
        ulid: { type: string, nullable: true }
        udiDiData: { type: object, nullable: true, description: "Seems null in basic UDI response, structure unknown if present." }
        manufacturer: { $ref: '#/components/schemas/ActorSummary', nullable: true }
        authorisedRepresentative: { $ref: '#/components/schemas/AuthorisedRepresentativeSummary', nullable: true }
        active: { type: boolean, nullable: true, description: "Purpose unclear, often null or reflects specific legacy rules." }
        administeringMedicine: { type: boolean, nullable: true }
        animalTissues: { type: boolean, nullable: true }
        nbDecision: { $ref: '#/components/schemas/NbDecision', nullable: true }
        basicUdi: { $ref: '#/components/schemas/DiIdentifier', nullable: true }
        companionDiagnostics: { type: boolean, nullable: true }
        reagent: { type: boolean, nullable: true }
        instrument: { type: boolean, nullable: true }
        professionalTesting: { type: boolean, nullable: true }
        kit: { type: boolean, nullable: true }
        device: { type: boolean, nullable: true, description: "True if System/Procedure Pack is selected during registration." }
        multiComponent: { $ref: '#/components/schemas/MultiComponentInfo', nullable: true }
        deviceCriterion: { type: string, nullable: true, description: "e.g., STANDARD, LEGACY, CUSTOM_MADE" }
        basicUdiType: { type: string, nullable: true, description: "Purpose unclear, always null in provided samples." }
        deviceModel: { type: string, nullable: true }
        deviceModelApplicable: { type: boolean, nullable: true }
        deviceName: { type: string, nullable: true }
        humanTissues: { type: boolean, nullable: true }
        medicinalProduct: { type: boolean, nullable: true }
        implantable: { type: boolean, nullable: true }
        sutures: { type: boolean, nullable: true }
        legislation: { $ref: '#/components/schemas/LegislationInfo', nullable: true }
        measuringFunction: { type: boolean, nullable: true }
        microbialSubstances: { type: boolean, nullable: true }
        nearPatientTesting: { type: boolean, nullable: true }
        reusable: { type: boolean, nullable: true }
        riskClass: { $ref: '#/components/schemas/RefDataCode', nullable: true }
        selfTesting: { type: boolean, nullable: true }
        specialDeviceType: { type: string, nullable: true, description: "Refers to Annex XVI device types for MDR/IVDR. Maybe RefDataCode?" }
        specialDeviceTypeApplicable: { type: boolean, nullable: true }
        medicalPurpose: { $ref: '#/components/schemas/TranslatedText', nullable: true, description: "Intended purpose description." }
        typeExaminationApplicable: { type: boolean, nullable: true }
        deviceCertificateInfoList: { type: array, nullable: true, items: { $ref: '#/components/schemas/DeviceCertificateInfo' } }
        deviceCertificateInfoListForDisplay: { type: array, nullable: true, items: { $ref: '#/components/schemas/DeviceCertificateInfo' }, description: "Likely redundant, same as deviceCertificateInfoList." }
        clinicalInvestigationApplicable: { type: boolean, nullable: true }
        clinicalInvestigationLinks: { type: array, nullable: true, items: { $ref: '#/components/schemas/ClinicalInvestigationLink' } }
        versionDate: { type: string, format: date-time, nullable: true }
        versionState: { $ref: '#/components/schemas/VersionState', nullable: true }
        latestVersion: { type: boolean, nullable: true }
        versionNumber: { type: integer, nullable: true }
        legacyDeviceUdiDiApplicable: { type: boolean, nullable: true, description: "Indicates if legacy devices under MDD/AIMDD/IVDD can be linked." }
        discardedDate: { type: string, format: date-time, nullable: true }
        linkedSscp: { $ref: '#/components/schemas/LinkedSscp', nullable: true }
        new: { type: boolean, nullable: true, description: "Internal flag, likely indicates newly created record." }

    DeviceDetails:
      description: Detailed information about a specific medical device (UDI-DI level). Based on samples ending in '-di.json'. All properties are nullable as per user request.
      type: object
      properties:
        uuid: { type: string, format: uuid, nullable: true }
        ulid: { type: string, nullable: true }
        primaryDi: { $ref: '#/components/schemas/DiIdentifier', nullable: true }
        containedItem: { type: object, nullable: true, description: "Structure unknown, null in provided samples." }
        marketInfoLink: { $ref: '#/components/schemas/MarketInfoLink', nullable: true }
        secondaryDi: { $ref: '#/components/schemas/DiIdentifier', nullable: true }
        secondaryDiApplicable: { type: boolean, nullable: true }
        additionalDescription: { $ref: '#/components/schemas/TranslatedText', nullable: true }
        tradeName: { $ref: '#/components/schemas/TranslatedText', nullable: true }
        tradeNameApplicable: { type: boolean, nullable: true }
        annexXVIApplicable: { type: boolean, nullable: true }
        brainElectroStimulation: { type: boolean, nullable: true }
        cmrSubstance: { type: boolean, nullable: true }
        cmrSubstances: { type: array, nullable: true, items: { $ref: '#/components/schemas/SubstanceInfo' }, description: "List of CMR substances." }
        componentDis: { type: array, nullable: true, items: { $ref: '#/components/schemas/DiIdentifier' }, description: "List of component DIs for System/Procedure Pack." }
        contactLenses: { type: boolean, nullable: true }
        directMarking: { type: boolean, nullable: true }
        directMarkingSameAsUdiDi: { type: boolean, nullable: true }
        directMarkingDi: { $ref: '#/components/schemas/DiIdentifier', nullable: true }
        emr: { type: boolean, nullable: true } # Electromagnetic radiation?
        endocrineDisruptingSubstances: { type: boolean, nullable: true } # Distinct from endocrineDisruptor?
        endocrineDisruptor: { type: boolean, nullable: true }
        equipmentForAdiposeTissue: { type: boolean, nullable: true }
        fillingByInjection: { type: boolean, nullable: true }
        humanProductSubstances: { type: boolean, nullable: true }
        latex: { type: boolean, nullable: true }
        udiPiType: { $ref: '#/components/schemas/UdiPiType', nullable: true }
        medicinalProductSubstances: { type: boolean, nullable: true }
        cndNomenclatures: { type: array, nullable: true, items: { $ref: '#/components/schemas/CndNomenclature' } }
        oemApplicable: { type: boolean, nullable: true }
        productDesigner: { type: object, nullable: true, description: "Structure unknown, null in provided samples." }
        placedOnTheMarket: { $ref: '#/components/schemas/Country', nullable: true }
        productsToBeIntroduced: { type: boolean, nullable: true }
        reference: { type: string, nullable: true, description: "Catalogue number or reference." }
        reprocessed: { type: boolean, nullable: true }
        maxNumberOfReuses: { type: integer, nullable: true }
        maxNumberOfReusesApplicable: { type: boolean, nullable: true }
        singleUse: { type: boolean, nullable: true }
        clinicalSizeApplicable: { type: boolean, nullable: true }
        clinicalSizes: { type: array, nullable: true, items: { $ref: '#/components/schemas/ClinicalSize' } }
        deviceStatus: { $ref: '#/components/schemas/DeviceStatus', nullable: true }
        subStatuses: { type: array, nullable: true, items: { $ref: '#/components/schemas/RefDataCode' }, description: "Additional status flags, e.g., suspended." }
        sterile: { type: boolean, nullable: true }
        sterilization: { type: boolean, nullable: true, description: "Needs sterilization before use?" }
        storageApplicable: { type: boolean, nullable: true }
        storageHandlingConditions: { type: array, nullable: true, items: { $ref: '#/components/schemas/StorageHandlingCondition' } }
        unitOfUse: { type: object, nullable: true, description: "Unit of Use DI information. Structure unknown, null in provided samples." }
        criticalWarningsApplicable: { type: boolean, nullable: true }
        criticalWarnings: { type: array, nullable: true, items: { $ref: '#/components/schemas/CriticalWarning' } }
        additionalInformationUrl: { type: string, format: uri, nullable: true }
        storageSymbol: { type: object, nullable: true, description: "Structure unknown, null in provided samples." }
        baseQuantity: { type: integer, nullable: true, description: "Quantity in the base package." }
        versionDate: { type: string, format: date-time, nullable: true }
        versionState: { $ref: '#/components/schemas/VersionState', nullable: true }
        latestVersion: { type: boolean, nullable: true }
        versionNumber: { type: integer, nullable: true }
        discardedDate: { type: string, format: date-time, nullable: true }
        linkedUdiDiView: { $ref: '#/components/schemas/LinkedUdiDiView', nullable: true }
        new: { type: boolean, nullable: true, description: "Internal flag, likely indicates newly created record." }

    # --- Reusable Helper Schemas (Refined) ---
    RefDataCode:
      description: Represents a reference data code object. All properties are nullable as per user request.
      type: object
      nullable: true # The whole object can be null
      properties:
        code: { type: string, nullable: true, description: "The reference data code value (e.g., 'refdata.risk-class.class-a')." }

    ActorSummary:
      description: Basic summary of an Actor (Manufacturer, Notified Body, Authorised Representative). All properties are nullable as per user request.
      type: object
      nullable: true # The whole object can be null
      properties:
        uuid: { type: string, format: uuid, nullable: true }
        ulid: { type: string, nullable: true }
        versionNumber: { type: integer, nullable: true }
        versionState: { $ref: '#/components/schemas/VersionState', nullable: true }
        latestVersion: { type: boolean, nullable: true }
        lastUpdateDate: { type: string, format: date-time, nullable: true }
        name: { type: string, nullable: true }
        names: { $ref: '#/components/schemas/TranslatedText', nullable: true }
        abbreviatedNames: { $ref: '#/components/schemas/TranslatedText', nullable: true }
        actorType: { $ref: '#/components/schemas/ActorType', nullable: true }
        status: { $ref: '#/components/schemas/RefDataCode', nullable: true }
        statusFromDate: { type: string, format: date, nullable: true }
        countryIso2Code: { type: string, nullable: true }
        countryName: { type: string, nullable: true }
        countryType: { type: string, nullable: true }
        geographicalAddress: { type: string, nullable: true }
        electronicMail: { type: string, format: email, nullable: true }
        telephone: { type: string, nullable: true }
        srn: { type: string, nullable: true }

    ActorType:
      description: Type classification for an actor. All properties are nullable as per user request.
      type: object
      nullable: true # The whole object can be null
      properties:
        code: { type: string, nullable: true } # e.g., refdata.actor-type.manufacturer
        srnCode: { type: string, nullable: true } # e.g., MF, AR, NB
        category: { type: string, nullable: true } # e.g., ECONOMIC_ENTITY, SUPERVISING_ENTITY

    AuthorisedRepresentativeSummary:
      description: Specific summary for an Authorised Representative linked to a Manufacturer. All properties are nullable as per user request.
      type: object
      nullable: true # The whole object can be null
      properties:
        nonEuManufacturerUuid: { type: string, format: uuid, nullable: true }
        authorisedRepresentativeUuid: { type: string, format: uuid, nullable: true }
        authorisedRepresentativeUlid: { type: string, nullable: true }
        name: { type: string, nullable: true }
        srn: { type: string, nullable: true }
        address: { type: string, nullable: true }
        countryName: { type: string, nullable: true }
        startDate: { type: string, format: date, nullable: true }
        endDate: { type: string, format: date, nullable: true }
        terminationDate: { type: string, format: date, nullable: true }
        email: { type: string, format: email, nullable: true }
        telephone: { type: string, nullable: true }
        mandateStatus: { type: object, nullable: true, description: "Structure unknown, null in samples." } # Could be RefDataCode?
        actorStatus: { $ref: '#/components/schemas/RefDataCode', nullable: true } # Assuming RefDataCode
        actorStatusFromDate: { type: string, format: date, nullable: true }
        versionNumber: { type: integer, nullable: true }
        versionState: { $ref: '#/components/schemas/VersionState', nullable: true }
        latestVersion: { type: boolean, nullable: true }
        lastUpdateDate: { type: string, format: date-time, nullable: true }
        ulid: { type: string, nullable: true } # Repeated? Verify if same as authorisedRepresentativeUlid

    DeviceCertificateInfo:
      description: Information about a specific device certificate. All properties are nullable as per user request.
      type: object
      nullable: true # The whole object can be null
      properties:
        uuid: { type: string, format: uuid, nullable: true }
        ulid: { type: string, nullable: true }
        certificateExpiry: { type: string, format: date, nullable: true }
        certificateNumber: { type: string, nullable: true }
        certificateRevision: { type: string, nullable: true }
        notifiedBody: { $ref: '#/components/schemas/ActorSummary', nullable: true }
        certificateType: { $ref: '#/components/schemas/RefDataCode', nullable: true }
        versionState: { $ref: '#/components/schemas/VersionState', nullable: true }
        latestVersion: { type: boolean, nullable: true }
        versionNumber: { type: integer, nullable: true }
        discardedDate: { type: string, format: date-time, nullable: true }
        issueDate: { type: string, format: date, nullable: true }
        status: { $ref: '#/components/schemas/RefDataCode', nullable: true } # Assuming RefDataCode, e.g., refdata.certificate-status.issued
        nbProvidedCertificate: { type: boolean, nullable: true }
        startingValidityDate: { type: string, format: date, nullable: true }
        new: { type: boolean, nullable: true }

    DiIdentifier:
      description: Represents a Device Identifier (DI) like Primary DI, Basic UDI, Direct Marking DI. All properties are nullable as per user request.
      type: object
      nullable: true # The whole object can be null
      properties:
        uuid: { type: string, format: uuid, nullable: true }
        code: { type: string, nullable: true }
        issuingAgency: { $ref: '#/components/schemas/RefDataCode', nullable: true }
        type: { type: string, nullable: true, description: "e.g., PRIMARY_DI, BASIC_UDI, SECONDARY_DI" }
        new: { type: boolean, nullable: true }

    LinkedUdiDiView:
      description: Information about a linked UDI-DI record (e.g., legacy link). Structure uncertain based on samples. All properties are nullable as per user request.
      type: object
      nullable: true # Samples show this can be null
      properties:
        uuid: { type: string, format: uuid , nullable: true} # Certificate UUID? Device UUID?
        ulid: { type: string, nullable: true }
        versionNumber: { type: integer, nullable: true }
        udiDi: { $ref: '#/components/schemas/DiIdentifier', nullable: true }
        basicUdiUuid: { type: string, format: uuid , nullable: true}
        basicUdiDi: { $ref: '#/components/schemas/DiIdentifier', nullable: true }
        manualLink: { type: boolean, nullable: true }
        deviceCriterion: { type: string, nullable: true } # e.g., "LEGACY"
        deviceLinkedOnDate: { type: string, format: date, nullable: true }

    StorageHandlingCondition:
      description: Describes a specific storage or handling condition. All properties are nullable as per user request.
      type: object
      nullable: true # The whole object can be null
      properties:
        uuid: { type: string, format: uuid, nullable: true }
        typeCode: { type: string, nullable: true, description: "e.g., refdata.storage-handling-conditions-type.SHC013" }
        mandatory: { type: boolean, nullable: true }
        description: { $ref: '#/components/schemas/TranslatedText', nullable: true }
        udiDiDataId: { type: integer, nullable: true, description: "Link back to the parent UDI DI data record ID?" }
        new: { type: boolean, nullable: true }

    CriticalWarning:
      description: Describes a specific critical warning. All properties are nullable as per user request.
      type: object
      nullable: true # The whole object can be null

    # --- Actor Schemas ---
    ActorSearchResponse:
      description: Response from the actor search endpoint.
      type: object
      properties:
        content:
          type: array
          items:
            $ref: '#/components/schemas/ActorSummary'
        pageable:
          $ref: '#/components/schemas/Pageable'
        totalPages:
          type: integer
        totalElements:
          type: integer
        last:
          type: boolean
        size:
          type: integer
        number:
          type: integer
        sort:
          $ref: '#/components/schemas/Sort'
        numberOfElements:
          type: integer
        first:
          type: boolean
        empty:
          type: boolean

    ActorDetailResponse:
      description: Detailed information about an actor.
      type: object
      properties:
        uuid:
          type: string
          format: uuid
        ulid:
          type: string
        name:
          type: string
        names:
          $ref: '#/components/schemas/TranslatedText'
        abbreviatedName:
          type: string
          nullable: true
        abbreviatedNames:
          $ref: '#/components/schemas/TranslatedText'
          nullable: true
        actorType:
          $ref: '#/components/schemas/ActorType'
        actorStatus:
          $ref: '#/components/schemas/RefDataCode'
        actorStatusFromDate:
          type: string
          format: date-time
          nullable: true
        countryIso2Code:
          type: string
        countryName:
          type: string
        countryType:
          type: string
        dateOfRegistration:
          type: string
          format: date-time
        eudamedIdentifier:
          type: string
        electronicMail:
          type: string
          format: email
        telephone:
          type: string
        geographicalAddress:
          type: string
        buildingNumber:
          type: string
          nullable: true
        streetName:
          type: string
          nullable: true
        postbox:
          type: string
          nullable: true
        addressComplement:
          type: string
          nullable: true
        postalZone:
          type: string
        cityName:
          type: string
        latestVersion:
          type: boolean
        versionNumber:
          type: integer
        associatedToUser:
          type: boolean
        registrationUlid:
          type: string
        legislationLinks:
          type: array
          items:
            $ref: '#/components/schemas/LegislationLink'
          nullable: true
        selectable:
          type: boolean
        importers:
          type: array
          items:
            $ref: '#/components/schemas/ImporterInfo'
          nullable: true
          description: "List of importers associated with this actor (only applicable for manufacturers)"
        nonEuManufacturers:
          type: array
          items:
            $ref: '#/components/schemas/ActorSummary'
          nullable: true
          description: "List of non-EU manufacturers associated with this actor (only applicable for importers and authorized representatives)"
        mandates:
          type: array
          items:
            $ref: '#/components/schemas/Mandate'
          nullable: true
        mandateHistory:
          type: array
          items:
            $ref: '#/components/schemas/MandateHistory'
          nullable: true
        subcontractors:
          type: array
          items:
            $ref: '#/components/schemas/Subcontractor'
          nullable: true
        subcontractorHistory:
          type: array
          items:
            $ref: '#/components/schemas/SubcontractorHistory'
          nullable: true
        certificates:
          type: array
          items:
            $ref: '#/components/schemas/Certificate'
          nullable: true
        actorDataPublicView:
          $ref: '#/components/schemas/ActorDataPublicView'
          nullable: true
          description: "Public view data for the actor"

    Pageable:
      description: Pagination information.
      type: object
      properties:
        sort:
          $ref: '#/components/schemas/Sort'
        offset:
          type: integer
        pageNumber:
          type: integer
        pageSize:
          type: integer
        paged:
          type: boolean
        unpaged:
          type: boolean

    Sort:
      description: Sorting information.
      type: object
      properties:
        sorted:
          type: boolean
        unsorted:
          type: boolean
        empty:
          type: boolean

    LegislationLink:
      description: Link to legislation.
      type: object
      properties:
        uuid:
          type: string
          format: uuid
        legislation:
          $ref: '#/components/schemas/RefDataCode'
        new:
          type: boolean

    Mandate:
      description: Mandate information.
      type: object
      properties:
        uuid:
          type: string
          format: uuid
        ulid:
          type: string
        mandator:
          $ref: '#/components/schemas/ActorSummary'
        mandatee:
          $ref: '#/components/schemas/ActorSummary'
        startDate:
          type: string
          format: date
        endDate:
          type: string
          format: date
          nullable: true
        status:
          $ref: '#/components/schemas/RefDataCode'
        versionNumber:
          type: integer
        versionState:
          $ref: '#/components/schemas/VersionState'
        latestVersion:
          type: boolean
        lastUpdateDate:
          type: string
          format: date-time

    MandateHistory:
      description: Mandate history information.
      type: object
      properties:
        uuid:
          type: string
          format: uuid
        ulid:
          type: string
        mandator:
          $ref: '#/components/schemas/ActorSummary'
        mandatee:
          $ref: '#/components/schemas/ActorSummary'
        startDate:
          type: string
          format: date
        endDate:
          type: string
          format: date
          nullable: true
        status:
          $ref: '#/components/schemas/RefDataCode'
        versionNumber:
          type: integer
        versionState:
          $ref: '#/components/schemas/VersionState'
        latestVersion:
          type: boolean
        lastUpdateDate:
          type: string
          format: date-time

    Subcontractor:
      description: Subcontractor information.
      type: object
      properties:
        uuid:
          type: string
          format: uuid
        ulid:
          type: string
        contractor:
          $ref: '#/components/schemas/ActorSummary'
        subcontractor:
          $ref: '#/components/schemas/ActorSummary'
        startDate:
          type: string
          format: date
        endDate:
          type: string
          format: date
          nullable: true
        status:
          $ref: '#/components/schemas/RefDataCode'
        versionNumber:
          type: integer
        versionState:
          $ref: '#/components/schemas/VersionState'
        latestVersion:
          type: boolean
        lastUpdateDate:
          type: string
          format: date-time

    SubcontractorHistory:
      description: Subcontractor history information.
      type: object
      properties:
        uuid:
          type: string
          format: uuid
        ulid:
          type: string
        contractor:
          $ref: '#/components/schemas/ActorSummary'
        subcontractor:
          $ref: '#/components/schemas/ActorSummary'
        startDate:
          type: string
          format: date
        endDate:
          type: string
          format: date
          nullable: true
        status:
          $ref: '#/components/schemas/RefDataCode'
        versionNumber:
          type: integer
        versionState:
          $ref: '#/components/schemas/VersionState'
        latestVersion:
          type: boolean
        lastUpdateDate:
          type: string
          format: date-time

    Certificate:
      description: Certificate information.
      type: object
      properties:
        uuid:
          type: string
          format: uuid
        ulid:
          type: string
        certificateNumber:
          type: string
        certificateType:
          $ref: '#/components/schemas/RefDataCode'
        certificateStatus:
          $ref: '#/components/schemas/RefDataCode'
        issueDate:
          type: string
          format: date
        expiryDate:
          type: string
          format: date
          nullable: true
        versionNumber:
          type: integer
        versionState:
          $ref: '#/components/schemas/VersionState'
        latestVersion:
          type: boolean
        lastUpdateDate:
          type: string
          format: date-time
        description: { $ref: '#/components/schemas/TranslatedText', nullable: true }
        udiDiDataId: { type: integer, nullable: true, description: "Link back to the parent UDI DI data record ID?" }
        new: { type: boolean, nullable: true }

    TranslatedText:
      description: Represents text that can have multiple language translations. All properties are nullable as per user request.
      type: object
      nullable: true # Can be null (e.g., abbreviatedNames)
      properties:
        texts: { type: array, nullable: true, items: { $ref: '#/components/schemas/TextEntry' } }

    TextEntry:
      description: A single text entry, potentially with language information. All properties are nullable as per user request.
      type: object
      nullable: true # The whole object can be null
      properties:
        language: { $ref: '#/components/schemas/LanguageInfo', nullable: true }
        text: { type: string, nullable: true }
        allLanguagesApplicable: { type: boolean, nullable: true }

    LanguageInfo:
      description: ISO language code and name. All properties are nullable as per user request.
      type: object
      nullable: true # Can be null if allLanguagesApplicable is true
      properties:
        isoCode: { type: string, nullable: true }
        name: { type: string, nullable: true }

    VersionState:
      description: Represents the version state of an entity. All properties are nullable as per user request.
      type: object
      nullable: true # Can be null
      properties:
        code: { type: string, nullable: true, description: "e.g., 'refdata.eudamed-entity-version-status.registered'" }

    NbDecision:
      description: Notified Body decision details. Structure uncertain. All properties are nullable as per user request.
      type: object
      nullable: true # Can be null
      properties:
        uuid: { type: string, format: uuid, nullable: true }
        reason: { $ref: '#/components/schemas/RefDataCode', nullable: true }
        actor: { type: object, nullable: true, description: "Structure unknown, null in sample." } # Could be ActorSummary?
        date: { type: string, format: date, nullable: true }
        new: { type: boolean, nullable: true }

    ClinicalInvestigationLink:
      description: Details of a linked clinical investigation. All properties are nullable as per user request.
      type: object
      nullable: true # Can be null within the list
      properties:
        uuid: { type: string, format: uuid, nullable: true }
        ulid: { type: string, nullable: true }
        clinicalInvestigationId: { type: string, nullable: true } # CIV ID
        clinicalInvestigationNumber: { type: string, nullable: true } # User-defined number?
        clinicalInvestigationRegistered: { type: boolean, nullable: true }
        insideEu: { type: boolean, nullable: true }
        countries: { type: array, nullable: true, items: { $ref: '#/components/schemas/Country' } }
        versionState: { $ref: '#/components/schemas/VersionState', nullable: true }
        latestVersion: { type: boolean, nullable: true }
        versionNumber: { type: integer, nullable: true }
        discardedDate: { type: string, format: date-time, nullable: true }
        new: { type: boolean, nullable: true }

    LinkedSscp:
      description: Details of a linked Summary of Safety and Clinical Performance (SSCP). All properties are nullable as per user request.
      type: object
      nullable: true # Can be null
      properties:
        uuid: { type: string, format: uuid, nullable: true }
        ulid: { type: string, nullable: true }
        validated: { type: boolean, nullable: true }
        referenceNumber: { type: string, nullable: true }
        revisionNumber: { type: string, nullable: true }
        issueDate: { type: string, format: date, nullable: true }
        versionNumber: { type: integer, nullable: true }
        inactive: { type: boolean, nullable: true }
        inactiveStatusDate: { type: string, format: date, nullable: true }

    MarketInfoLink:
      description: Information about market availability. All properties are nullable as per user request.
      type: object
      nullable: true # Can be null
      properties:
        uuid: { type: string, format: uuid, nullable: true }
        ulid: { type: string, nullable: true }
        msWhereAvailable: { type: array, nullable: true, items: { $ref: '#/components/schemas/MarketAvailability' } }
        versionState: { $ref: '#/components/schemas/VersionState', nullable: true }
        latestVersion: { type: boolean, nullable: true }
        versionNumber: { type: integer, nullable: true }
        discardedDate: { type: string, format: date-time, nullable: true }
        versionDate: { type: string, format: date-time, nullable: true }
        new: { type: boolean, nullable: true }

    MarketAvailability:
      description: Availability details for a specific market (country). All properties are nullable as per user request.
      type: object
      nullable: true # Can be null within the list
      properties:
        uuid: { type: string, format: uuid, nullable: true }
        marketInfoLinkId: { type: integer, nullable: true }
        country: { $ref: '#/components/schemas/Country', nullable: true }
        startDate: { type: string, format: date, nullable: true }
        endDate: { type: string, format: date, nullable: true }
        new: { type: boolean, nullable: true }

    Country:
      description: Represents a country with its details. All properties are nullable as per user request.
      type: object
      nullable: true # Can be null (e.g., placedOnTheMarket)
      properties:
        name: { type: string, nullable: true }
        type: { type: string, nullable: true, description: "e.g., EU_MEMBER_STATE, NON_EU, EU_EXTENDED, EU_SPECIAL" }
        iso2Code: { type: string, nullable: true }
        nonEUMemberState: { type: boolean, nullable: true }

    UdiPiType:
      description: Specifies which Production Identifiers (PI) are used. All properties are nullable as per user request.
      type: object
      nullable: true # Can be null
      properties:
        batchNumber: { type: boolean, nullable: true }
        serializationNumber: { type: boolean, nullable: true }
        manufacturingDate: { type: boolean, nullable: true }
        expirationDate: { type: boolean, nullable: true }
        softwareIdentification: { type: boolean, nullable: true }

    CndNomenclature:
      description: Represents a CND (Classificazione Nazionale Dispositivi medici) nomenclature entry. All properties are nullable as per user request.
      type: object
      nullable: true # Can be null within the list
      properties:
        uuid: { type: string, nullable: true, description: "Format might be specific, not standard UUID" }
        code: { type: string, nullable: true }
        description: { $ref: '#/components/schemas/TranslatedText', nullable: true }
        gmdnNomenclatures: { type: array, nullable: true, items: { $ref: '#/components/schemas/GmdnNomenclature' }, description: "Associated GMDN terms." }

    GmdnNomenclature: # Added based on CndNomenclature structure
      description: Represents a GMDN nomenclature entry (structure guessed). All properties are nullable as per user request.
      type: object
      nullable: true # Can be null within the list
      properties:
        uuid: { type: string, nullable: true }
        code: { type: string, nullable: true }
        description: { $ref: '#/components/schemas/TranslatedText', nullable: true }
        # Add other GMDN fields if known

    SubstanceInfo: # Added based on DeviceDetails `cmrSubstances`
      description: Information about a substance (e.g., CMR). Structure guessed. All properties are nullable as per user request.
      type: object
      nullable: true # Can be null within the list
      properties:
        name: { type: string, nullable: true }
        concentration: { type: number, nullable: true }
        unit: { type: string, nullable: true } # e.g., '% w/w'
        # Add other relevant fields like CAS number, EC number etc. if known

    ClinicalSize:
      description: Represents the clinical size information for a device. All properties are nullable as per user request.
      type: object
      nullable: true # Can be null within the list
      properties:
        uuid: { type: string, format: uuid, nullable: true }
        text: { type: string, nullable: true } # Free text value?
        value: { type: number, nullable: true } # Specific numeric value?
        udiDiDataId: { type: integer, nullable: true }
        minimumValue: { type: number, nullable: true }
        maximumValue: { type: number, nullable: true }
        type: { $ref: '#/components/schemas/RefDataCode', nullable: true } # e.g., refdata.clinical-size-type.CST32
        precision: { $ref: '#/components/schemas/RefDataCode', nullable: true } # e.g., refdata.clinical-size-precision.range
        metricOfMeasurement: { $ref: '#/components/schemas/RefDataCode', nullable: true } # e.g., refdata.unit-of-measurement.MU03
        clinicalSizeTypeDescription: { type: string, nullable: true } # Purpose unclear, null in sample
        measuringUnitDescription: { type: string, nullable: true } # Purpose unclear, null in sample
        new: { type: boolean, nullable: true }

    DeviceStatus:
      description: Represents the status of the device model. All properties are nullable as per user request.
      type: object
      nullable: true # Can be null
      properties:
        uuid: { type: string, format: uuid, nullable: true }
        type: { $ref: '#/components/schemas/RefDataCode', nullable: true } # e.g., refdata.device-model-status.on-the-market
        statusDate: { type: string, format: date, nullable: true }
        new: { type: boolean, nullable: true }

    LegislationInfo: # Added based on BasicUdiData draft
      description: Information about the applicable legislation. All properties are nullable as per user request.
      type: object
      nullable: true # The whole object can be null
      properties:
        code: { type: string, nullable: true } # e.g., "refdata.applicable-legislation.ivdr"
        legacyDirective: { type: boolean, nullable: true }

    MultiComponentInfo: # Added based on BasicUdiData draft
      description: Information if the device is part of a system or procedure pack. All properties are nullable as per user request.
      type: object
      nullable: true # The whole object can be null
      properties:
        code: { type: string, nullable: true } # e.g., "refdata.multi-component.system"
        criterion: { type: string, nullable: true } # e.g., "STANDARD", "LEGACY"

    ActorAddress:
      description: Detailed address information for an actor.
      type: object
      nullable: true # The whole object can be null
      properties:
        streetName: { type: string, nullable: true }
        streetInfoApplicable: { type: boolean, nullable: true }
        buildingNumber: { type: string, nullable: true }
        complement: { type: string, nullable: true }
        postbox: { type: string, nullable: true }
        gps: { type: string, nullable: true }
        cityName: { type: string, nullable: true }
        postalZone: { type: string, nullable: true }
        country: { $ref: '#/components/schemas/Country', nullable: true }

    RegulatoryComplianceResponsible:
      description: Information about a person responsible for regulatory compliance.
      type: object
      nullable: true # The whole object can be null
      properties:
        firstName: { type: string, nullable: true }
        familyName: { type: string, nullable: true }
        electronicMail: { type: string, format: email, nullable: true }
        telephone: { type: string, nullable: true }
        position: { type: string, nullable: true }
        geographicalAddress: { $ref: '#/components/schemas/ActorAddress', nullable: true }

    AccuracyData:
      description: Information about the accuracy verification of actor data.
      type: object
      nullable: true # The whole object can be null
      properties:
        uuid: { type: string, format: uuid, nullable: true }
        accuracyDate: { type: string, format: date, nullable: true }
        new: { type: boolean, nullable: true }

    ImporterInfo:
      description: Information about an importer associated with a manufacturer.
      type: object
      nullable: true # The whole object can be null
      properties:
        uuid: { type: string, format: uuid, nullable: true, description: "UUID of the importer relationship" }
        ulid: { type: string, nullable: true, description: "ULID of the importer relationship" }
        startDate: { type: string, format: date-time, nullable: true, description: "Start date of the importer relationship" }
        endDate: { type: string, format: date-time, nullable: true, description: "End date of the importer relationship, null if still active" }
        relatedActorSrn: { type: string, nullable: true, description: "SRN of the related actor" }
        relatedActorName: { type: string, nullable: true, description: "Name of the related actor" }
        relatedActorCountryIsoCode: { type: string, nullable: true, description: "Country ISO code of the related actor" }
        relatedActorAddress: { type: string, nullable: true, description: "Address of the related actor" }
        relatedActorEmail: { type: string, format: email, nullable: true, description: "Email of the related actor" }
        relatedActorPhone: { type: string, nullable: true, description: "Phone number of the related actor" }
        actor: { $ref: '#/components/schemas/ActorSummary', nullable: true, description: "Detailed information about the importer actor" }

    ActorDataPublicView:
      description: Public view data for an actor.
      type: object
      nullable: true # The whole object can be null
      properties:
        uuid: { type: string, format: uuid, nullable: true }
        ulid: { type: string, nullable: true }
        type: { $ref: '#/components/schemas/ActorType', nullable: true }
        actorStatus: { $ref: '#/components/schemas/RefDataCode', nullable: true }
        actorStatusFromDate: { type: string, format: date-time, nullable: true }
        country: { $ref: '#/components/schemas/Country', nullable: true }
        name: { $ref: '#/components/schemas/TranslatedText', nullable: true }
        abbreviatedName: { $ref: '#/components/schemas/TranslatedText', nullable: true }
        europeanVatNumberApplicable: { type: boolean, nullable: true }
        europeanVatNumber: { type: string, nullable: true }
        eudamedIdentifier: { type: string, nullable: true }
        tradeRegister: { type: string, nullable: true }
        eori: { type: string, nullable: true }
        organizationIdentificationDocuments: { type: array, items: { type: object }, nullable: true }
        authorisedRepresentatives: { type: array, items: { $ref: '#/components/schemas/AuthorisedRepresentativeSummary' }, nullable: true }
        competentAuthorityResponsibility: { type: object, nullable: true }
        telephone: { type: string, nullable: true }
        electronicMail: { type: string, format: email, nullable: true }
        website: { type: string, format: uri, nullable: true }
        actorAddress: { $ref: '#/components/schemas/ActorAddress', nullable: true }
        validatorName: { type: string, nullable: true }
        validatorUuid: { type: string, format: uuid, nullable: true }
        validatorType: { $ref: '#/components/schemas/ActorType', nullable: true }
        validatorSrn: { type: string, nullable: true }
        validatorAddress: { $ref: '#/components/schemas/ActorAddress', nullable: true }
        validatorEmail: { type: string, format: email, nullable: true }
        validatorTelephone: { type: string, nullable: true }
        regulatoryComplianceResponsibles: { type: array, items: { $ref: '#/components/schemas/RegulatoryComplianceResponsible' }, nullable: true }
        legislationLinks: { type: array, items: { $ref: '#/components/schemas/LegislationLink' }, nullable: true }
        latestSubsidiary: { type: object, nullable: true }
        certificates: { type: array, items: { $ref: '#/components/schemas/Certificate' }, nullable: true }
        latestVersion: { type: boolean, nullable: true }
        versionNumber: { type: integer, nullable: true }
        versionState: { $ref: '#/components/schemas/VersionState', nullable: true }
        lastUpdateDate: { type: string, format: date-time, nullable: true }
        accuracyData: { type: array, items: { $ref: '#/components/schemas/AccuracyData' }, nullable: true }
        lastAccuracyDate: { type: string, format: date, nullable: true }
