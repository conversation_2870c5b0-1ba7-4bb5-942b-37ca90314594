import fetch from 'node-fetch';

const EUDAMED_API_BASE_URL = 'https://ec.europa.eu/tools/eudamed';

// Helper function to simplify refdata codes
function simplifyRefdata(value) {
  if (typeof value === 'string' && value.startsWith('refdata.')) {
    const parts = value.split('.');
    return parts[parts.length - 1];
  }
  return value;
}

// Recursive function to process object properties for filtering and refdata simplification
function processObject(obj) {
  if (obj === null || typeof obj !== 'object') {
    return obj;
  }

  if (Array.isArray(obj)) {
    return obj.map(item => processObject(item)).filter(item => item !== null && item !== '' && item !== undefined);
  }

  const processedObj = {};
  for (const [key, value] of Object.entries(obj)) {
    const processedValue = processObject(value);
    if (processedValue !== null && processedValue !== '' && processedValue !== undefined) {
      processedObj[key] = simplifyRefdata(processedValue);
    }
  }
  // Filter out empty objects after processing
  if (Object.keys(processedObj).length === 0) {
      return undefined;
  }
  return processedObj;
}

// Generic function to fetch data from EUDAMED API
async function fetchEudamedData(endpoint, params = {}, language = 'en') {
  const url = new URL(`${EUDAMED_API_BASE_URL}${endpoint}`);
  Object.entries(params).forEach(([key, value]) => {
    if (value !== null && value !== undefined) {
      url.searchParams.append(key, value);
    }
  });

  // Append language parameter if not already in params
  if (!params.languageIso2Code) {
      url.searchParams.append('languageIso2Code', language);
  }

  try {
    const response = await fetch(url.toString());
    const data = await response.json();

    if (!response.ok) {
      console.error('EUDAMED API Error Response:', data);
      return {
        error: {
          timestamp: new Date().toISOString(),
          status: response.status,
          error: data.error || 'EUDAMED API Error',
          message: data.message || 'An error occurred while fetching data from EUDAMED.',
          eudamedResponse: data,
          path: endpoint,
        }
      };
    }

    return {data: processObject(data)};

  } catch (error) {
    console.error(`Error fetching data from EUDAMED endpoint ${endpoint}:`, error);
    return {
       error: {
        timestamp: new Date().toISOString(),
        status: 500,
        error: 'Internal Server Error',
        message: 'An error occurred while processing your request.',
        path: endpoint,
      }
    };
  }
}

// Mapping function for Device data (summary from search)
function mapDeviceSummary(eudamedDevice) {
  if (!eudamedDevice)
    return null;

  const device = {
    uuid: eudamedDevice.uuid,
    basicUdi: eudamedDevice.basicUdi,
    udiDi: eudamedDevice.primaryDi,
    risk: eudamedDevice.riskClass?.code,
    name: eudamedDevice.tradeName,
    reference: eudamedDevice.reference,
    status: eudamedDevice.deviceStatusType?.code,
    versionNumber: eudamedDevice.versionNumber,
    manufacturer: {
      srn: eudamedDevice.manufacturerSrn,
      name: eudamedDevice.manufacturerName,
      status: eudamedDevice.manufacturerStatus?.code,
    },
     authorisedRepresentative: {
      srn: eudamedDevice.authorisedRepresentativeSrn,
      name: eudamedDevice.authorisedRepresentativeName,
    },
  };

  const processedDevice = processObject(device)
  return processedDevice;
}

// Define possible risk combinations based on actors/[srn]/risks/+server.js
const riskCombinations = [
    { legislation: 'mdd', riskClass: 'class-i', risk: 'mdd-i' },
    { legislation: 'mdd', riskClass: 'class-iia', risk: 'mdd-iia' },
    { legislation: 'mdd', riskClass: 'class-iib', risk: 'mdd-iib' },
    { legislation: 'mdd', riskClass: 'class-iii', risk: 'mdd-iii' },
    { legislation: 'mdr', riskClass: 'class-i', risk: 'mdr-i' },
    { legislation: 'mdr', riskClass: 'class-iia', risk: 'mdr-iia' },
    { legislation: 'mdr', riskClass: 'class-iib', risk: 'mdr-iib' },
    { legislation: 'mdr', riskClass: 'class-iii', risk: 'mdr-iii' },
    { legislation: 'aimdd', riskClass: 'aimdd', risk: 'aimdd' },
    { legislation: 'ivdr', riskClass: 'class-a', risk: 'ivdr-a' },
    { legislation: 'ivdr', riskClass: 'class-b', risk: 'ivdr-b' },
    { legislation: 'ivdr', riskClass: 'class-c', risk: 'ivdr-c' },
    { legislation: 'ivdr', riskClass: 'class-d', risk: 'ivdr-d' },
    { legislation: 'ivdd', riskClass: 'ivd-annex-ii-list-a', risk: 'ivdd-a' },
    { legislation: 'ivdd', riskClass: 'ivd-annex-ii-list-b', risk: 'ivdd-b' },
    { legislation: 'ivdd', riskClass: 'ivd-devices-self-testing', risk: 'ivdd-testing' },
    { legislation: 'ivdd', riskClass: 'ivd-general', risk: 'ivdd-general' },
];

// Helper function to safely get nested property value
function getNestedValue(obj, path) {
    const parts = path.split('.');
    let current = obj;
    for (const part of parts) {
        if (current === null || current === undefined) {
            return undefined;
        }
        current = current[part];
    }
    return current;
}

// Mapping function to combine data from multiple endpoints
function mapCombinedDeviceData(searchData, basicUdiData, deviceDetailsData) {
    const targetDevice = {};

    // Mapping based on device_mapping.md and priority: search > basicUdi > details
    // Identification
    targetDevice.uuid = getNestedValue(searchData, 'content.0.uuid') || getNestedValue(deviceDetailsData, 'uuid');
    targetDevice.basicUdi = getNestedValue(searchData, 'content.0.basicUdi') || getNestedValue(basicUdiData, 'basicUdi.code');
    targetDevice.primaryDi = getNestedValue(searchData, 'content.0.primaryDi') || getNestedValue(deviceDetailsData, 'primaryDi.code');
    targetDevice.secondaryDi = getNestedValue(deviceDetailsData, 'secondaryDi.code');
    targetDevice.directMarkingDi = getNestedValue(deviceDetailsData, 'directMarkingDi.code');
    targetDevice.legislation = getNestedValue(searchData, 'content.0.applicableLegislation') || getNestedValue(basicUdiData, 'legislation.code');

    // riskClass: Use riskCombinations for mapping
    const riskClassCode = getNestedValue(searchData, 'content.0.riskClass.code') || getNestedValue(basicUdiData, 'riskClass.code');
    const legislationCode = targetDevice.legislation; // Use the mapped legislation

    const matchedRisk = riskCombinations.find(rc =>
        rc.legislation === legislationCode && rc.riskClass === riskClassCode
    );

    if (matchedRisk) {
        targetDevice.riskClass = matchedRisk.risk;
    } else if (riskClassCode) {
        // Fallback to simplified risk class code if combination not found
        targetDevice.riskClass = simplifyRefdata(riskClassCode);
    } else {
        targetDevice.riskClass = undefined; // Or null, depending on desired output for no risk class
    }

    targetDevice.device = getNestedValue(basicUdiData, 'device');
    targetDevice.specialDeviceType = getNestedValue(basicUdiData, 'specialDeviceType');

    // Manufacturer information
    targetDevice.manufacturer = {
        uuid: getNestedValue(basicUdiData, 'manufacturer.uuid'),
        srn: getNestedValue(searchData, 'content.0.manufacturerSrn') || getNestedValue(basicUdiData, 'manufacturer.srn'),
        name: getNestedValue(searchData, 'content.0.manufacturerName') || getNestedValue(basicUdiData, 'manufacturer.name'),
        country: getNestedValue(basicUdiData, 'manufacturer.countryName'),
        address: getNestedValue(basicUdiData, 'manufacturer.geographicalAddress'),
        eMail: getNestedValue(basicUdiData, 'manufacturer.electronicMail'),
        phone: getNestedValue(basicUdiData, 'manufacturer.telephone'),
        status: getNestedValue(searchData, 'content.0.manufacturerStatus.code') || getNestedValue(basicUdiData, 'manufacturer.status.code'),
    };

    // Authorised Representative information
    targetDevice.authorisedRepresentative = {
        uuid: getNestedValue(basicUdiData, 'authorisedRepresentative.authorisedRepresentativeUuid'),
        srn: getNestedValue(searchData, 'content.0.authorisedRepresentativeSrn') || getNestedValue(basicUdiData, 'authorisedRepresentative.srn'),
        name: getNestedValue(searchData, 'content.0.authorisedRepresentativeName') || getNestedValue(basicUdiData, 'authorisedRepresentative.name'),
        country: getNestedValue(basicUdiData, 'authorisedRepresentative.countryName'),
        address: getNestedValue(basicUdiData, 'authorisedRepresentative.address'),
        email: getNestedValue(basicUdiData, 'authorisedRepresentative.email'),
        phone: getNestedValue(basicUdiData, 'authorisedRepresentative.telephone'),
        status: getNestedValue(basicUdiData, 'authorisedRepresentative.actorStatus.code'),
    };

    // Device names and descriptions
    targetDevice.deviceName = getNestedValue(searchData, 'content.0.deviceName') || getNestedValue(basicUdiData, 'deviceName');
    targetDevice.tradeNames = getNestedValue(deviceDetailsData, 'tradeName.texts');
    targetDevice.reference = getNestedValue(searchData, 'content.0.reference') || getNestedValue(deviceDetailsData, 'reference');
    targetDevice.placedOnTheMarket = getNestedValue(deviceDetailsData, 'placedOnTheMarket.name');

    // marketInfoLinks: Map from msWhereAvailable array in DeviceDetails
    const marketAvailability = getNestedValue(deviceDetailsData, 'marketInfoLink.msWhereAvailable');
    if (Array.isArray(marketAvailability)) {
        targetDevice.marketInfoLinks = marketAvailability.map(item => ({
            country: getNestedValue(item, 'country.name'),
            startDate: getNestedValue(item, 'startDate'),
            endDate: getNestedValue(item, 'endDate')
        }));
    } else {
        targetDevice.marketInfoLinks = [];
    }

    // Additional descriptions and information
    targetDevice.additionalDescriptions = getNestedValue(deviceDetailsData, 'additionalDescription.texts');
    targetDevice.additionalInformationUrl = getNestedValue(deviceDetailsData, 'additionalInformationUrl');

    // Booleans and simple values
    targetDevice.reprocessed = getNestedValue(deviceDetailsData, 'reprocessed');
    targetDevice.baseQuantity = getNestedValue(searchData, 'content.0.containerPackageCount') || getNestedValue(deviceDetailsData, 'baseQuantity');
    targetDevice.reusable = getNestedValue(basicUdiData, 'reusable');
    targetDevice.singleUse = getNestedValue(deviceDetailsData, 'singleUse');
    targetDevice.maxNumberOfReuses = getNestedValue(deviceDetailsData, 'maxNumberOfReuses');
    targetDevice.active = getNestedValue(basicUdiData, 'active');
    targetDevice.administeringMedicine = getNestedValue(basicUdiData, 'administeringMedicine');
    targetDevice.animalTissues = getNestedValue(basicUdiData, 'animalTissues');
    targetDevice.annexXVIApplicable = getNestedValue(deviceDetailsData, 'annexXVIApplicable');
    targetDevice.companionDiagnostics = getNestedValue(basicUdiData, 'companionDiagnostics');
    targetDevice.endocrineDisruptor = getNestedValue(deviceDetailsData, 'endocrineDisruptor');
    targetDevice.humanTissues = getNestedValue(basicUdiData, 'humanTissues');
    targetDevice.implantable = getNestedValue(basicUdiData, 'implantable');
    targetDevice.instrument = getNestedValue(basicUdiData, 'instrument');
    targetDevice.kit = getNestedValue(basicUdiData, 'kit');
    targetDevice.latex = getNestedValue(deviceDetailsData, 'latex');
    targetDevice.measuringFunction = getNestedValue(basicUdiData, 'measuringFunction');
    targetDevice.medicinalProduct = getNestedValue(basicUdiData, 'medicinalProduct');
    targetDevice.microbialSubstances = getNestedValue(basicUdiData, 'microbialSubstances');
    targetDevice.nearPatientTesting = getNestedValue(basicUdiData, 'nearPatientTesting');
    targetDevice.oemApplicable = getNestedValue(deviceDetailsData, 'oemApplicable');
    targetDevice.professionalTesting = getNestedValue(basicUdiData, 'professionalTesting');
    targetDevice.reagent = getNestedValue(basicUdiData, 'reagent');
    targetDevice.selfTesting = getNestedValue(basicUdiData, 'selfTesting');
    targetDevice.sterile = getNestedValue(searchData, 'content.0.sterile') || getNestedValue(deviceDetailsData, 'sterile');
    targetDevice.sterilization = getNestedValue(deviceDetailsData, 'sterilization');
    targetDevice.typeExaminationApplicable = getNestedValue(basicUdiData, 'typeExaminationApplicable');

    // Process the final object to remove null/empty values and simplify refdata codes
    return processObject(targetDevice);
}

export { fetchEudamedData, mapDeviceSummary, processObject, simplifyRefdata, getNestedValue, riskCombinations, mapCombinedDeviceData };
