import { fetchEudamedData, mapDeviceSummary, mapCombinedDeviceData } from './eudamedApi.js';
import cliProgress from 'cli-progress';
import { writeFileSync } from 'fs';

class RetryProcessor {
  constructor(options = {}) {
    this.concurrencyLimit = options.concurrencyLimit || 5;
    this.language = options.language || 'en';
    this.progressBar = null;
  }

  async retryFailedRequests(database, options = {}) {
    const maxRetries = options.maxRetries || 3;
    const filterResource = options.filterResource || null;

    console.log('🔍 Analyzing failed requests...');

    // Get failed requests from database
    const failedRequests = database.getApiRequestErrors(filterResource, maxRetries);

    if (failedRequests.length === 0) {
      console.log('✅ No failed requests found to retry.');
      return {
        success: true,
        totalRequests: 0,
        successfulRetries: 0,
        failedRetries: 0,
        skippedRequests: 0
      };
    }

    console.log(`📊 Found ${failedRequests.length} failed requests to retry`);
    console.log(`   • deviceSummary: ${failedRequests.filter(r => r.resource === 'deviceSummary').length}`);
    console.log(`   • deviceUDIDI: ${failedRequests.filter(r => r.resource === 'deviceUDIDI').length}`);
    console.log(`   • deviceBasicUDI: ${failedRequests.filter(r => r.resource === 'deviceBasicUDI').length}`);
    console.log('');

    // Initialize progress bar
    this.progressBar = new cliProgress.SingleBar({
      format: 'Retrying |{bar}| {percentage}% | {value}/{total} requests | ETA: {eta}s',
      barCompleteChar: '\u2588',
      barIncompleteChar: '\u2591',
      hideCursor: true
    });

    this.progressBar.start(failedRequests.length, 0);

    let successfulRetries = 0;
    let failedRetries = 0;
    let skippedRequests = 0;

    // Process requests in batches with concurrency control
    for (let i = 0; i < failedRequests.length; i += this.concurrencyLimit) {
      const batch = failedRequests.slice(i, i + this.concurrencyLimit);

      const batchPromises = batch.map(async (request, index) => {
        try {
          const result = await this.retryRequest(request, database);

          // Update progress bar
          this.progressBar.update(i + index + 1);

          return result;
        } catch (error) {
          console.error(`Error retrying request ${request.id}:`, error);
          this.progressBar.update(i + index + 1);
          return { success: false, error: error.message, request };
        }
      });

      const batchResults = await Promise.all(batchPromises);

      // Process batch results
      for (const result of batchResults) {
        if (result.success) {
          successfulRetries++;
          // Remove successful request from error table
          this.removeSuccessfulRequest(database, result.request.id);
        } else if (result.skipped) {
          skippedRequests++;
        } else {
          failedRetries++;
          // Update retry count and last_retry_at
          this.updateRetryCount(database, result.request.id);
        }
      }
    }

    this.progressBar.stop();

    console.log('\n✅ Retry process completed!');
    console.log('===========================');
    console.log(`Total requests processed: ${failedRequests.length}`);
    console.log(`Successful retries: ${successfulRetries}`);
    console.log(`Failed retries: ${failedRetries}`);
    console.log(`Skipped requests: ${skippedRequests}`);

    return {
      success: true,
      totalRequests: failedRequests.length,
      successfulRetries,
      failedRetries,
      skippedRequests
    };
  }

  async retryRequest(request, database) {
    try {
      switch (request.resource) {
        case 'deviceSummary':
          return await this.retryDeviceSummaryRequest(request, database);
        case 'deviceUDIDI':
          return await this.retryDeviceUDIDIRequest(request, database);
        case 'deviceBasicUDI':
          return await this.retryDeviceBasicUDIRequest(request, database);
        default:
          console.error(`Unknown resource type: ${request.resource}`);
          return { success: false, error: 'Unknown resource type', request };
      }
    } catch (error) {
      return { success: false, error: error.message, request };
    }
  }

  async retryDeviceSummaryRequest(request, database) {
    // For deviceSummary failures, retry the failed page AND adjacent pages
    // to account for dynamic page content changes
    const pageRange = this.parsePageRange(request.range_param);
    const pageSize = request.page_size || 300;

    let successCount = 0;
    let errorCount = 0;

    for (const pageNumber of pageRange) {
      // Also try the page before and after (if they exist and are reasonable)
      const pagesToTry = [pageNumber];
      if (pageNumber > 0) pagesToTry.unshift(pageNumber - 1);
      if (pageNumber < 10000) pagesToTry.push(pageNumber + 1); // reasonable upper limit

      for (const page of pagesToTry) {
        try {
          const eudamedParams = {
            page: page.toString(),
            size: pageSize.toString(),
          };

          const { data, error } = await fetchEudamedData('/api/devices/udiDiData', eudamedParams, this.language);

          if (error) {
            errorCount++;
            continue;
          }

          // Map devices using mapDeviceSummary function
          let mappedDevices = [];
          if (data.content && Array.isArray(data.content)) {
            mappedDevices = data.content
              .filter(device => device && device.primaryDi)
              .map(device => mapDeviceSummary(device))
              .filter(device => device !== null);
          }

          // Save devices to database
          if (mappedDevices.length > 0) {
            const savedCount = database.saveDevices(mappedDevices);
            successCount += savedCount;
          }

        } catch (error) {
          errorCount++;
        }
      }
    }

    return {
      success: successCount > 0,
      savedCount: successCount,
      errorCount,
      request
    };
  }

  async retryDeviceUDIDIRequest(request, database) {
    try {
      const { data: deviceDetailsData, error } = await fetchEudamedData(
        `/api/devices/udiDiData/${request.uuid}`,
        {},
        this.language
      );

      if (error) {
        return { success: false, error, request };
      }

      // Process and save the device details data
      // This would need to be integrated with the existing device details processing logic
      // For now, we'll just mark it as successful if we got data
      return {
        success: true,
        data: deviceDetailsData,
        request
      };

    } catch (error) {
      return { success: false, error: error.message, request };
    }
  }

  async retryDeviceBasicUDIRequest(request, database) {
    try {
      const { data: basicUdiData, error } = await fetchEudamedData(
        `/api/devices/basicUdiData/udiDiData/${request.uuid}`,
        {},
        this.language
      );

      if (error) {
        return { success: false, error, request };
      }

      // Process and save the basic UDI data
      // This would need to be integrated with the existing device details processing logic
      // For now, we'll just mark it as successful if we got data
      return {
        success: true,
        data: basicUdiData,
        request
      };

    } catch (error) {
      return { success: false, error: error.message, request };
    }
  }

  parsePageRange(rangeParam) {
    if (!rangeParam) return [];

    // Handle simple page number
    if (/^\d+$/.test(rangeParam)) {
      return [parseInt(rangeParam)];
    }

    // Handle range like "1-10"
    if (/^\d+-\d+$/.test(rangeParam)) {
      const [start, end] = rangeParam.split('-').map(n => parseInt(n));
      return Array.from({ length: end - start + 1 }, (_, i) => start + i);
    }

    // For more complex ranges, just return the first number found
    const match = rangeParam.match(/\d+/);
    return match ? [parseInt(match[0])] : [];
  }

  removeSuccessfulRequest(database, requestId) {
    try {
      const sql = 'DELETE FROM api_request_errors WHERE id = ?';
      database.db.run(sql, [requestId]);

      // Save database to file
      const data = database.db.export();
      writeFileSync(database.dbPath, data);
    } catch (error) {
      console.error('Error removing successful request:', error);
    }
  }

  updateRetryCount(database, requestId) {
    try {
      const sql = `
        UPDATE api_request_errors
        SET retry_count = retry_count + 1,
            last_retry_at = datetime('now'),
            updated_at = datetime('now')
        WHERE id = ?
      `;
      database.db.run(sql, [requestId]);

      // Save database to file
      const data = database.db.export();
      writeFileSync(database.dbPath, data);
    } catch (error) {
      console.error('Error updating retry count:', error);
    }
  }
}

export { RetryProcessor };
