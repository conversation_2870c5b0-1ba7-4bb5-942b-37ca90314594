# Device Data Mapping

This document describes the mapping of data from the EUDAMED API endpoints to the target `device.json` structure. The data is retrieved by calling three endpoints in sequence: 1 `searchDevicesUdiDiData`, 2 `getBasicUdiDataByDeviceUuid`, and 3 `getDeviceDetailsByUuid`. In case of data conflicts, the priority order is 1, then 2, then 3.

| target | source searchDevicesUdiDiData | source getBasicUdiDataByDeviceUuid | source getDeviceDetailsByUuid | example | mapping comment |
|---|---|---|---|---|---|
| uuid | uuid | - | uuid | 8e1dd8ba-22b3-4697-88ca-2fcfc1fb04af | Prioritized from `searchDevicesUdiDiData`. |
| basicUdi | basicUdi | basicUdi.code | - | ++G226TotalRNAPKitT5 | Prioritized from `searchDevicesUdiDiData`. |
| primaryDi | primaryDi | - | primaryDi.code | 04049055003891 | Prioritized from `searchDevicesUdiDiData`. |
| secondaryDi | - | - | secondaryDi.code | 04049055003891 |  |
| directMarkingDi | - | - | directMarkingDi.code | ++G226DX172000. |  |
| udiPiType | - | - | udiPiType | {"batchNumber": true, "expirationDate": "2024-11-01"} |  |
| legislation | applicableLegislation | legislation.code | - | refdata.applicable-legislation.ivdr | Prioritized from `searchDevicesUdiDiData`. |
| riskClass | riskClass.code | riskClass.code | - | ivdr-a | Prioritized from `searchDevicesUdiDiData`. Target format is a combination of risk class and legislation. |
| device | - | device | - | true |  |
| specialDeviceType | - | specialDeviceType | - | refdata.special-mdd-device-type.orthopedic |  |
| manufacturer.uuid | - | manufacturer.uuid | - | 7c0b3331-8eea-4e2c-bdbb-a3e994246e23 |  |
| manufacturer.srn | manufacturerSrn | manufacturer.srn | - | CA-MF-000024034 | Prioritized from `searchDevicesUdiDiData`. |
| manufacturer.name | manufacturerName | manufacturer.name | - | Norgen Biotek Corporation | Prioritized from `searchDevicesUdiDiData`. |
| manufacturer.country | - | manufacturer.countryName | - | Canada | Mapped from `countryName` in `BasicUdiData`. |
| manufacturer.address | - | manufacturer.geographicalAddress | - | 3430 Schmon Parkway Thorold | Mapped from `geographicalAddress` in `BasicUdiData`. |
| manufacturer.eMail | - | manufacturer.electronicMail | - | <EMAIL> | Mapped from `electronicMail` in `BasicUdiData`. |
| manufacturer.phone | - | manufacturer.telephone | - | 9052278848 | Mapped from `telephone` in `BasicUdiData`. |
| manufacturer.status | manufacturerStatus.code | manufacturer.status.code | - | refdata.actor-status.active | Prioritized from `searchDevicesUdiDiData`. |
| authorisedRepresentative.uuid | - | authorisedRepresentative.authorisedRepresentativeUuid | - | aa09f930-5024-425a-96dd-ff9e17626209 |  |
| authorisedRepresentative.srn | authorisedRepresentativeSrn | authorisedRepresentative.srn | - | NL-AR-000000116 | Prioritized from `searchDevicesUdiDiData`. |
| authorisedRepresentative.name | authorisedRepresentativeName | authorisedRepresentative.name | - | Emergo Europe B.V. | Prioritized from `searchDevicesUdiDiData`. |
| authorisedRepresentative.country | - | authorisedRepresentative.countryName | - | Netherlands | Mapped from `countryName` in `BasicUdiData`. |
| authorisedRepresentative.address | - | authorisedRepresentative.address | - | 60 Westervoortsedijk Arnhem |  |
| authorisedRepresentative.email | - | authorisedRepresentative.email | - | <EMAIL> |  |
| authorisedRepresentative.phone | - | authorisedRepresentative.telephone | - | +31 70 345 8570 |  |
| authorisedRepresentative.status | - | authorisedRepresentative.actorStatus.code | - | refdata.actor-status.active | Mapped from `actorStatus.code` in `BasicUdiData`. |
| deviceName | deviceName | deviceName | - | Total RNA Purification Kit Dx | Prioritized from `searchDevicesUdiDiData`. |
| tradeNames | tradeName | - | tradeName.texts | [{"language": "en", "text": "GENERIC HBV VIRAL LOAD VER 2.0"}] | Prioritized from `searchDevicesUdiDiData` for simple string, but target is array from `DeviceDetails`. Using `DeviceDetails` structure. |
| reference | reference | - | reference | Dx17200 | Prioritized from `searchDevicesUdiDiData`. |
| deviceModel | deviceModel | deviceModel | - | 1001 | Prioritized from `searchDevicesUdiDiData`. |
| placedOnTheMarket | - | - | placedOnTheMarket.name | Netherlands | Mapped from `name` in `DeviceDetails`. |
| marketInfoLinks | - | - | marketInfoLink.msWhereAvailable | [{"country": {"name": "Croatia", "type": "EU_MEMBER_STATE", "iso2Code": "HR", "nonEUMemberState": false}, "startDate": "2024-11-01", "endDate": "2024-11-01"}] | Mapped from `msWhereAvailable` array in `DeviceDetails`. Only consider the countryName, startDate and endDate during the mapping |
| additionalDescriptions | - | - | additionalDescription.texts | [{"language": "en", "text": "GENERIC HBV VIRAL LOAD ..."}] | Mapped from `texts` array in `DeviceDetails`. |
| additionalInformationUrl | - | - | additionalInformationUrl | https://ipd2004.com/en/ifu |  |
| reprocessed | - | - | reprocessed | false |  |
| baseQuantity | containerPackageCount | - | baseQuantity | 1 | Prioritized from `searchDevicesUdiDiData`. |
| reusable | - | reusable | - | false |  |
| singleUse | - | - | singleUse | true |  |
| maxNumberOfReuses | - | - | maxNumberOfReuses | 1 |  |
| active | - | active | - | false |  |
| administeringMedicine | - | administeringMedicine | - | false |  |
| animalTissues | - | animalTissues | - | false |  |
| annexXVIApplicable | - | - | annexXVIApplicable | false |  |
| companionDiagnostics | - | companionDiagnostics | - | false |  |
| endocrineDisruptor | - | - | endocrineDisruptor | false |  |
| humanProduct | - | - | - | false | Not found in provided schemas. Assuming default value. |
| humanTissues | - | humanTissues | - | false |  |
| implantable | - | implantable | - | true |  |
| instrument | - | instrument | - | false |  |
| kit | - | kit | - | true |  |
| latex | - | - | latex | false |  |
| measuringFunction | - | measuringFunction | - | false |  |
| medicinalProduct | - | medicinalProduct | - | false |  |
| microbialSubstances | - | microbialSubstances | - | false |  |
| nearPatientTesting | - | nearPatientTesting | - | false |  |
| oemApplicable | - | - | oemApplicable | false |  |
| professionalTesting | - | professionalTesting | - | true |  |
| reagent | - | reagent | - | true |  |
| selfTesting | - | selfTesting | - | false |  |
| sterile | sterile | - | sterile | false | Prioritized from `searchDevicesUdiDiData`. |
| sterilization | - | - | sterilization | false |  |
| typeExaminationApplicable | - | typeExaminationApplicable | - | true |  |
| multiComponent | multiComponent | multiComponent | - | {"code": "refdata.multi-component.system", "criterion": "STANDARD"} | Prioritized from `searchDevicesUdiDiData`. |
| componentDis | - | - | componentDis | [{"uuid": "00000000-0000-0000-0000-000000000003", "code": "COMPONENT-DI-ABC"}] | Mapped from `componentDis` array in `DeviceDetails`. |
| clinicalInvestigationLinks | - | clinicalInvestigationLinks | - | [{"uuid": "00000000-0000-0000-0000-000000000002", "clinicalInvestigationId": "CIV-ABC-123", "clinicalInvestigationNumber": "CI-001", "clinicalInvestigationRegistered": true, "insideEu": true, "countries": [{"name": "Germany", "type": "EU_MEMBER_STATE", "iso2Code": "DE", "nonEUMemberState": false}], "versionState": {"code": "refdata.eudamed-entity-version-status.registered"}, "versionNumber": 1, "discardedDate": null, "new": false}] | Mapped from `clinicalInvestigationLinks` array in `BasicUdiData`.|
| clinicalSizes | - | - | clinicalSizes | [{"uuid": "00000000-0000-0000-0000-000000000004", "text": "Medium", "value": null, "udiDiDataId": null, "minimumValue": null, "maximumValue": null, "type": {"code": "refdata.clinical-size-type.CST01"}, "precision": null, "metricOfMeasurement": null, "clinicalSizeTypeDescription": null, "measuringUnitDescription": null, "new": false}] | Mapped from `clinicalSizes` array in `DeviceDetails`. |
| cmrSubstances | - | - | cmrSubstances | [{"name": "Example CMR Substance", "concentration": 0.1, "unit": "% w/w"}] | Mapped from `cmrSubstances` array in `DeviceDetails`. |
| criticalWarnings | - | - | criticalWarnings | [{"typeCode": "refdata.critical-warnings-type.CW001", "mandatory": true, "description": null, "udiDiDataId": null, "new": false}] | Mapped from `criticalWarnings` array in `DeviceDetails`. |
| deviceCertificateInfoList | - | deviceCertificateInfoList | - | [{"uuid": "00000000-0000-0000-0000-000000000001", "certificateExpiry": "2026-12-31", "certificateNumber": "CERT-12345", "certificateRevision": "1", "notifiedBody": null, "certificateType": {"code": "refdata.certificate-type.ce"}, "versionState": {"code": "refdata.eudamed-entity-version-status.registered"}, "latestVersion": true, "versionNumber": 1, "discardedDate": null, "issueDate": "2021-12-31", "status": {"code": "refdata.certificate-status.issued"}, "nbProvidedCertificate": true, "startingValidityDate": "2022-01-01", "new": false}] | Mapped from `deviceCertificateInfoList` array in `BasicUdiData`. |
| deviceCertificateInfoListForDisplay | - | deviceCertificateInfoListForDisplay | - | [{"uuid": "00000000-0000-0000-0000-000000000001", "certificateExpiry": "2026-12-31", "certificateNumber": "CERT-12345", "certificateRevision": "1", "notifiedBody": null, "certificateType": {"code": "refdata.certificate-type.ce"}, "versionState": {"code": "refdata.eudamed-entity-version-status.registered"}, "latestVersion": true, "versionNumber": 1, "discardedDate": null, "issueDate": "2021-12-31", "status": {"code": "refdata.certificate-status.issued"}, "nbProvidedCertificate": true, "startingValidityDate": "2022-01-01", "new": false}] | Mapped from `deviceCertificateInfoListForDisplay` array in `BasicUdiData`. |
| linkedSscp | - | linkedSscp | - | {"uuid": "7043cec1-f37b-4fe6-8c6d-9eb12d1b7f85", "validated": true, "referenceNumber": "TR004.2-250IC", "revisionNumber": "3", "issueDate": "2024-01-23", "versionNumber": 1, "inactive": false} |  |
| versionDate | lastUpdateDate | versionDate | versionDate | 2024-11-01T15:20:58.424 | Prioritized from `searchDevicesUdiDiData` (`lastUpdateDate`), but target field name is `versionDate`. |
| versionState | versionState | versionState | versionState | refdata.eudamed-entity-version-status.registered | Prioritized from `searchDevicesUdiDiData`. |
| discardedDate | - | discardedDate | discardedDate | 2024-11-01 | Prioritized from `getBasicUdiDataByDeviceUuid`. |
| versionNumber | versionNumber | versionNumber | versionNumber | 1 | Prioritized from `searchDevicesUdiDiData`. |
| deviceStatus | deviceStatusType.code | - | deviceStatus | {"type": "refdata.device-model-status.on-the-market", "statusDate": "2024-11-01"} | Prioritized from `searchDevicesUdiDiData` (`deviceStatusType.code`), but target is an object from `DeviceDetails`. Using `DeviceDetails` structure. |
| notifiedBodyDecision | - | nbDecision | - | {"reason": "refdata.decision-reason.approved", "date": "2025-02-03"} | Mapped from `nbDecision` in `BasicUdiData`. |
