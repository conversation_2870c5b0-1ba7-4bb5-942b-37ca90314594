# EUDAMED Device Extractor CLI

A standalone Node.js CLI tool that extracts device data from the EUDAMED API and saves it to a SQLite database. This tool replicates the functionality of the `/devices/all-udi` API endpoint with enhanced command-line interface and database storage capabilities.

## Features

- **Complete device extraction** using the same data processing logic as the existing API endpoint
- **Three extraction commands**:
  - **deviceSummary**: Fast extraction using 1 API call per page
  - **deviceDetails**: Comprehensive extraction using 3 API calls per device
  - **retry**: Retry failed API requests from the error tracking table
- **Flexible pagination** with support for page ranges, start/end pages, or full extraction
- **Parallel processing** with configurable concurrency limits (5-10 requests recommended)
- **SQLite database storage** using sql.js with upsert operations for conflict handling
- **Real-time progress monitoring** with cli-progress library
- **Error handling** with detailed console output and automatic retry capabilities
- **Test mode** to preview extraction without saving data
- **Database statistics** to monitor storage and progress

## Installation

1. Navigate to the CLI directory:
```bash
cd cli
```

2. Install dependencies:
```bash
npm install
```

3. Make the CLI executable (optional):
```bash
npm link
```

## Usage

The CLI tool uses a command-based architecture with three main operations:

### Commands

- **`deviceSummary`** - Extract device summary data (paginated device listing)
- **`deviceDetails`** - Extract comprehensive device details (3 API calls per device)
- **`retry`** - Retry failed API requests using the api_request_errors table

### Basic Usage

```bash
# Extract all devices using summary mode
node src/index.js deviceSummary

# Extract comprehensive device details
node src/index.js deviceDetails

# Retry failed requests
node src/index.js retry

# Or if linked globally
eudamed-extract deviceSummary
```

### Page Range Options

```bash
# Extract specific page range
node src/index.js deviceSummary --start-page 0 --end-page 10

# Extract specific pages using range syntax
node src/index.js deviceSummary --page-range "0-5,10,15-20"

# Extract from page 100 to the end
node src/index.js deviceSummary --start-page 100
```

### Performance Options

```bash
# Adjust concurrency (recommended: 5-10)
node src/index.js deviceSummary --concurrency 8

# Adjust page size (default: 300)
node src/index.js deviceSummary --page-size 300

# Combine performance settings
node src/index.js deviceSummary --concurrency 5 --page-size 200
```

### Output Options

```bash
# Specify custom database file
node src/index.js deviceSummary --output ./my-devices.db

# Show database statistics after extraction
node src/index.js deviceSummary --stats

# Preview extraction without saving (test mode)
node src/index.js deviceSummary --test
```

### Language Options

```bash
# Extract data in different language
node src/index.js deviceSummary --language de
```

### Retry Options

```bash
# Retry all failed requests
node src/index.js retry

# Set maximum retry attempts
node src/index.js retry --max-retries 5

# Retry only specific resource type
node src/index.js retry --filter-resource deviceSummary

# Custom retry concurrency
node src/index.js retry --concurrency 3

# Preview retry without executing
node src/index.js retry --test
```

### Extraction Modes

The CLI tool supports three main operations:

#### deviceSummary Command
- Uses 1 API call per page (`/api/devices/udiDiData`)
- Extracts basic device information (same as `/devices/all-udi` endpoint)
- Fast and efficient for large-scale extraction
- Saves data to `devices` table

#### deviceDetails Command
- **Double-loop architecture** for efficient processing:
  - **Outer loop**: Processes pages sequentially (one at a time)
  - **Inner loop**: Fetches device details in parallel within each page
- Uses 3 API calls per device:
  1. `/api/devices/udiDiData` (search data - from page fetch)
  2. `/api/devices/basicUdiData/{uuid}` (basic UDI data)
  3. `/api/devices/udiDiData/{uuid}` (detailed device data)
- Extracts comprehensive device information (same as `/devices/[udi_di]` endpoint)
- Includes nested objects like manufacturer details, market info, clinical data, etc.
- More predictable memory usage and better error isolation
- Saves data to `device_details` table

#### retry Command
- Reads failed requests from the `api_request_errors` table
- Retries each failed request based on its resource type
- For `deviceSummary` failures: Queries the failed page AND adjacent pages
- For `deviceUDIDI`/`deviceBasicUDI` failures: Retries the specific device UUID request
- Updates retry_count and removes successfully retried requests

```bash
# Extract comprehensive device details
node src/index.js deviceDetails

# Extract details with custom concurrency
node src/index.js deviceDetails --details-concurrency 2

# Extract details for specific pages
node src/index.js deviceDetails --start-page 0 --end-page 5

# Retry failed requests
node src/index.js retry --max-retries 3
```

## Command Line Options

### Common Options (All Commands)

| Option | Description | Default |
|--------|-------------|---------|
| `-o, --output <path>` | Output SQLite database file path | `./data/devices.db` |
| `-l, --language <code>` | Language code for API requests | `en` |
| `-t, --test` | Show what would be extracted without saving | `false` |
| `-s, --stats` | Show database statistics after extraction | `false` |

### deviceSummary & deviceDetails Options

| Option | Description | Default |
|--------|-------------|---------|
| `-s, --start-page <number>` | Starting page number (0-based) | `0` |
| `-e, --end-page <number>` | Ending page number (0-based, inclusive) | All pages |
| `-r, --page-range <range>` | Page range (e.g., "0-10,15,20-25") | - |
| `-c, --concurrency <number>` | Concurrency limit for parallel requests | `8` |
| `-p, --page-size <number>` | Number of devices per page | `300` |

### deviceDetails Specific Options

| Option | Description | Default |
|--------|-------------|---------|
| `-dc, --details-concurrency <number>` | Concurrency limit for device details requests | `3` |

### retry Command Options

| Option | Description | Default |
|--------|-------------|---------|
| `-m, --max-retries <number>` | Maximum retry attempts per request | `3` |
| `-f, --filter-resource <type>` | Filter by resource type (deviceSummary, deviceUDIDI, deviceBasicUDI) | All types |
| `-c, --concurrency <number>` | Concurrency limit for retry requests | `5` |

## Examples

### Device Summary Extraction
```bash
# Extract all devices with default settings
node src/index.js deviceSummary --output ./complete-devices.db --stats

# Extract first 50 pages for testing
node src/index.js deviceSummary --end-page 49 --output ./test-devices.db

# Continue from page 50
node src/index.js deviceSummary --start-page 50 --output ./test-devices.db

# High-performance extraction with lower concurrency
node src/index.js deviceSummary --concurrency 5 --page-size 300

# Extract specific pages only
node src/index.js deviceSummary --page-range "0-10,50-60,100-110"
```

### Device Details Extraction
```bash
# Extract comprehensive device details for all devices
node src/index.js deviceDetails --output ./detailed-devices.db --stats

# Extract details for first 10 pages with lower concurrency
node src/index.js deviceDetails --end-page 9 --details-concurrency 2

# Preview detailed extraction without saving
node src/index.js deviceDetails --test --start-page 0 --end-page 2
```

### Retry Failed Requests
```bash
# Retry all failed requests
node src/index.js retry --output ./my-devices.db

# Retry only deviceSummary failures with higher retry limit
node src/index.js retry --filter-resource deviceSummary --max-retries 5

# Preview retry operation without executing
node src/index.js retry --test --output ./my-devices.db

# Retry with custom concurrency
node src/index.js retry --concurrency 3 --output ./my-devices.db
```

## Database Schema

The SQLite database contains two tables depending on the extraction mode:

### Summary Mode: `devices` table

```sql
CREATE TABLE devices (
  uuid TEXT PRIMARY KEY,
  basicUdi TEXT,
  udiDi TEXT,
  risk TEXT,
  name TEXT,
  reference TEXT,
  status TEXT,
  versionNumber TEXT,
  manufacturer_srn TEXT,
  manufacturer_name TEXT,
  manufacturer_status TEXT,
  authorisedRepresentative_srn TEXT,
  authorisedRepresentative_name TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### Detailed Mode: `device_details` table

```sql
CREATE TABLE device_details (
  uuid TEXT PRIMARY KEY,
  basicUdi TEXT,
  primaryDi TEXT,
  secondaryDi TEXT,
  directMarkingDi TEXT,
  legislation TEXT,
  riskClass TEXT,
  device BOOLEAN,
  specialDeviceType TEXT,

  -- Manufacturer information (JSON)
  manufacturer TEXT,

  -- Authorised Representative information (JSON)
  authorisedRepresentative TEXT,

  -- Device names and descriptions
  deviceName TEXT,
  tradeNames TEXT, -- JSON array
  reference TEXT,
  placedOnTheMarket TEXT,
  marketInfoLinks TEXT, -- JSON array
  additionalDescriptions TEXT, -- JSON array
  additionalInformationUrl TEXT,

  -- Boolean flags for device characteristics
  reprocessed BOOLEAN,
  baseQuantity INTEGER,
  reusable BOOLEAN,
  singleUse BOOLEAN,
  maxNumberOfReuses INTEGER,
  active BOOLEAN,
  administeringMedicine BOOLEAN,
  animalTissues BOOLEAN,
  annexXVIApplicable BOOLEAN,
  companionDiagnostics BOOLEAN,
  endocrineDisruptor BOOLEAN,
  humanTissues BOOLEAN,
  implantable BOOLEAN,
  instrument BOOLEAN,
  kit BOOLEAN,
  latex BOOLEAN,
  measuringFunction BOOLEAN,
  medicinalProduct BOOLEAN,
  microbialSubstances BOOLEAN,
  nearPatientTesting BOOLEAN,
  oemApplicable BOOLEAN,
  professionalTesting BOOLEAN,
  reagent BOOLEAN,
  selfTesting BOOLEAN,
  sterile BOOLEAN,
  sterilization BOOLEAN,
  typeExaminationApplicable BOOLEAN,

  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### API Request Errors Table

The `api_request_errors` table tracks failed API requests for future retry functionality:

```sql
CREATE TABLE api_request_errors (
  id INTEGER PRIMARY KEY AUTOINCREMENT,
  resource TEXT NOT NULL CHECK (resource IN (
    'deviceSummary', 'deviceUDIDI', 'deviceBasicUDI'
  )),
  uuid TEXT,
  range_param TEXT,
  page_size INTEGER,
  error_message TEXT,
  http_status INTEGER,
  retry_count INTEGER DEFAULT 0,
  last_retry_at DATETIME,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  context TEXT
);
```

**Field Usage by Resource Type:**
- **deviceSummary**: Uses `range_param` (e.g., "1-10") and `page_size` for paginated requests
- **deviceUDIDI/deviceBasicUDI**: Uses `uuid` for individual device requests
- **All types**: Include `error_message`, `http_status`, and `retry_count` for error tracking

### Indexes
- **devices table**:
  - `idx_devices_udiDi` on `udiDi`
  - `idx_devices_basicUdi` on `basicUdi`
  - `idx_devices_manufacturer_srn` on `manufacturer_srn`
- **device_details table**:
  - `idx_device_details_primaryDi` on `primaryDi`
  - `idx_device_details_basicUdi` on `basicUdi`
  - `idx_device_details_legislation` on `legislation`
  - `idx_device_details_riskClass` on `riskClass`
- **errors table**:
  - `idx_errors_resource` on `resource`
  - `idx_errors_error_type` on `error_type`
  - `idx_errors_timestamp` on `timestamp`
  - `idx_errors_device_uuid` on `device_uuid`
- **api_request_errors table**:
  - `idx_api_request_errors_resource` on `resource`
  - `idx_api_request_errors_uuid` on `uuid`
  - `idx_api_request_errors_retry_count` on `retry_count`
  - `idx_api_request_errors_created_at` on `created_at`

## Error Handling

The tool provides comprehensive error handling:

- **Network errors**: Automatic retry and detailed error logging
- **API errors**: EUDAMED API error responses are logged with full details
- **Database errors**: SQLite constraint violations are handled gracefully
- **Validation errors**: Input validation with helpful error messages

Failed pages are reported at the end of extraction with specific error details.

## Performance Considerations

### Summary Mode
- **Concurrency**: Recommended range is 5-10 concurrent requests to balance speed and API stability
- **Page size**: Default 300 devices per page provides good balance of efficiency and memory usage
- **Memory**: Processes multiple pages in parallel with controlled memory usage

### Detailed Mode (Double-Loop Architecture)
- **Page processing**: Sequential (one page at a time) for predictable memory usage
- **Device details**: Parallel within each page (recommended concurrency: 2-5)
- **Memory efficiency**: Only one page worth of devices in memory at a time
- **Error isolation**: Failures on one page don't affect processing of other pages
- **Progress visibility**: Clear progress reporting for both page and device level

### Database Performance
- **Database**: Uses sql.js with file-based persistence for optimal SQLite performance
- **Batch operations**: Saves all devices from a page in a single transaction
- **Indexes**: Optimized indexes for both summary and detailed data queries

## Data Processing

The tool uses the same data mapping logic as the existing API endpoint:

1. **Fetches data** from `/api/devices/udiDiData` endpoint
2. **Filters devices** to include only those with `primaryDi`
3. **Maps device data** using `mapDeviceSummary` function
4. **Processes objects** to remove null/empty values and simplify refdata codes
5. **Saves to database** using upsert operations for conflict handling

## Troubleshooting

### Common Issues

1. **Permission errors**: Ensure write permissions for the output directory
2. **Network timeouts**: Reduce concurrency limit with `--concurrency 3`
3. **Database locked**: Ensure no other processes are using the database file
4. **Memory issues**: Reduce page size with `--page-size 100`

### Debug Mode

For detailed debugging, you can modify the source code to enable additional logging in the `eudamedApi.js` file by uncommenting the console.log statement on line 62.

## Testing

The CLI tool includes a comprehensive test suite to ensure reliability and prevent regressions.

### Running Tests

```bash
# Run all tests
npm test

# Run only unit tests
npm run test:unit

# Run only integration tests
npm run test:integration

# Run with verbose output
npm run test:verbose

# Run specific test
npm run test:version-conflict
```

### Test Categories

- **Unit Tests**: Test individual functions and modules in isolation
- **Integration Tests**: Test complete workflows and component interactions
- **Version Conflict Resolution**: Tests the database upsert logic that prevents older data from overwriting newer records

### Test Development

When making changes to the codebase:

1. **Run existing tests** to ensure no regressions: `npm test`
2. **Add new tests** for new functionality in `tests/unit/` or `tests/integration/`
3. **Update test documentation** in `tests/README.md`

See `tests/README.md` for detailed testing guidelines and coverage requirements.

## Dependencies

- **sql.js**: SQLite database operations compiled to WebAssembly
- **cli-progress**: Real-time progress bars and status updates
- **commander**: Command-line argument parsing and help generation
- **node-fetch**: HTTP client for EUDAMED API requests

## License

MIT License - see the main project for license details.
