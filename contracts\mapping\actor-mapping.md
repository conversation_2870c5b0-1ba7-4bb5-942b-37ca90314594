# Actor Mapping

This document describes the mapping of data from the EUDAMED `searchActors` and `getActorDetailsByUuid` endpoints to the target actor JSON structure (`documentation/analysis/expected output/actor.json`).

In case of data conflicts, data from the `searchActors` endpoint takes precedence over data from the `getActorDetailsByUuid` endpoint.

| target  | source searchActors  | source getActorDetailsByUuid  | example | mapping comment  |
|---------|----------------------|-------------------------------|---------|------------------|
| `uuid`  | `content[].uuid` | `uuid`  | `8e1dd8ba-22b3-4697-88ca-2fcfc1fb04af`  | Prioritize `searchActors` uuid.  |
| `srn` | `content[].srn`  | `actorDataPublicView.eudamedIdentifier` (assuming this is SRN) | `CA-MF-000024034` | Prioritize `searchActors` srn. The field in `getActorDetailsByUuid` is `eudamedIdentifier`, assuming this maps to SRN. |
| `vat` |  | `actorDataPublicView.europeanVatNumber` | `FR12345678901` | Available only in `getActorDetailsByUuid`. |
| `nationalTradeRegister` |  | `actorDataPublicView.tradeRegister` | `RC Paris B 123 456 789`  | Available only in `getActorDetailsByUuid`. |
| `eori`  |  | `actorDataPublicView.eori`  | `FR123456789000`  | Available only in `getActorDetailsByUuid`. |
| `actorType` | `content[].actorType.code`  | `actorType.code`  | `refdata.actor-type.manufacturer` | Map the `code` field from the `actorType` object. Prioritize `searchActors`. |
| `legislation` | `content[].applicableLegislation` | `legislationLinks[].legislation.code` | `refdata.applicable-legislation.ivdr` | Map the `applicableLegislation` from `searchActors`. If not available, map the `code` of the first object in the `legislationLinks` array from `getActorDetailsByUuid`. |
| `name`  | `content[].name` | `name`  | `Norgen Biotek Corporation` | Prioritize `searchActors` name.  |
| `names.abbreviated` |  | | `true`  | Indicates whether the record comes from abbreviatedNames (true) or from names (false) |
| `names.language`  |  | `names.texts[].language.isoCode` or `abbreviatedNames.texts[].language.isoCode`  | `en`  | Map the `isoCode` from the `language` object within the first `texts` entry in `getActorDetailsByUuid.names`.  |
| `names.name`  |  | `names.texts[].text` or `abbreviatedNames.texts[].text`  | `Norgen Biotek Corporation` | Prioritize `searchActors` name. If not available, map the `text` from the first `texts` entry in `getActorDetailsByUuid.names`.  |
| `status.startDate`  |  | `actorStatusFromDate` (assuming this is the start date) | `2025-04-05T13:14:38.173Z`  | The detailed status fields in the target are not directly available at the top level in source responses. This might require mapping from nested status objects if needed. |
| `status.endDate`  |  | | `2025-04-05T13:14:38.173Z`  | See comment for `status.startDate`.  |
| `status.versionNumber`  | `content[].versionNumber`  | `versionNumber` | `1` | Prioritize `searchActors` versionNumber. See comment for `status.startDate`. |
| `status.versionState` | `content[].versionState`  | `versionState.code` | `refdata.eudamed-entity-version-status.registered`  | Prioritize `searchActors` versionState. If not available, map `versionState.code` from `getActorDetailsByUuid`. See comment for `status.startDate`.  |
| `status.statusFromDate` |  | `actorStatusFromDate` | `2025-04-05`  | Available in `getActorDetailsByUuid`. See comment for `status.startDate`.  |
| `status.latestVersion`  | `content[].latestVersion` | `latestVersion` | `true`  | Prioritize `searchActors` latestVersion. See comment for `status.startDate`. |
| `status.lastUpdateDate` | `content[].lastUpdateDate`  | `lastUpdateDate`  | `2022-05-16T15:54:13.053` | Prioritize `searchActors` lastUpdateDate. See comment for `status.startDate`.  |
| `status.code` | `content[].actorStatus.code`  | `actorStatus.code`  | `refdata.actor-status.active` | Map the `code` field from the `actorStatus` object. Prioritize `searchActors`. See comment for `status.startDate`. |
| `address.countryIso2Code` | `content[].countryIso2Code` | `actorDataPublicView.actorAddress.country.iso2Code` | `CA`  | Prioritize `searchActors` countryIso2Code. If not available, map from `getActorDetailsByUuid`. |
| `address.countryName` | `content[].countryName` | `actorDataPublicView.actorAddress.country.name` | `Canada`  | Prioritize `searchActors` countryName. If not available, map from `getActorDetailsByUuid`. |
| `address.geographicalAddress` | `content[].geographicalAddress` | `actorDataPublicView.actorAddress.geographicalAddress`  | `3430 Schmon Parkway Thorold` | Prioritize `searchActors` geographicalAddress. If not available, map from `getActorDetailsByUuid`. |
| `address.buildingNumber`  |  | `actorDataPublicView.actorAddress.buildingNumber` | `10`  | Available only in `getActorDetailsByUuid`. |
| `address.streetName`  |  | `actorDataPublicView.actorAddress.streetName` | `Main Street` | Available only in `getActorDetailsByUuid`. |
| `address.postbox` |  | `actorDataPublicView.actorAddress.postbox`  | `P.O. Box 123`  | Available only in `getActorDetailsByUuid`. |
| `address.addressComplement` |  | `actorDataPublicView.actorAddress.complement` | `Suite 400` | Available only in `getActorDetailsByUuid`. The field name is `complement` in the source. |
| `address.postalZone`  |  | `actorDataPublicView.actorAddress.postalZone` | `L2V 4Y6` | Available only in `getActorDetailsByUuid`. |
| `address.cityName`  |  | `actorDataPublicView.actorAddress.cityName` | `Thorold` | Available only in `getActorDetailsByUuid`. |
| `address.gps` |  | `actorDataPublicView.actorAddress.gps`  | `43.111, -79.222` | Available only in `getActorDetailsByUuid`. |
| `contact.firstName` |  | `actorDataPublicView.regulatoryComplianceResponsibles[].firstName` | `John`  | Map from the first entry in `regulatoryComplianceResponsibles` in `getActorDetailsByUuid`. This assumes the first entry is the primary contact. |
| `contact.familyName`  |  | `actorDataPublicView.regulatoryComplianceResponsibles[].familyName` | `Doe` | Map from the first entry in `regulatoryComplianceResponsibles` in `getActorDetailsByUuid`. This assumes the first entry is the primary contact. |
| `contact.electronicMail`  | `content[].electronicMail`  | `actorDataPublicView.electronicMail`  | `<EMAIL>` | Prioritize `searchActors` electronicMail. If not available, map from `getActorDetailsByUuid`.  |
| `contact.telephone` | `content[].telephone` | `actorDataPublicView.telephone` | `9052278848`  | Prioritize `searchActors` telephone. If not available, map from `getActorDetailsByUuid`. |
| `contact.website` |  | `actorDataPublicView.website` | `http://www.norgenbiotek.com` | Available only in `getActorDetailsByUuid`. |
| `authorisedRepresentatives[].uuid` | | `actorDataPublicView.authorisedRepresentatives[].authorisedRepresentativeUuid` | `string` | Map from the `authorisedRepresentativeUuid` field. |
| `authorisedRepresentatives[].srn` | | `actorDataPublicView.authorisedRepresentatives[].srn` | `string` | Map from the `srn` field. |
| `authorisedRepresentatives[].actorType` | | **Source Missing in AuthorisedRepresentativeSummary** | `string` | Source for actor type code is missing. Target structure suggests mapping from `actorType.code`. |
| `authorisedRepresentatives[].name` | | `actorDataPublicView.authorisedRepresentatives[].name` | `string` | Map from the `name` field. |
| `authorisedRepresentatives[].countryIsoCode` | | **Source Missing in AuthorisedRepresentativeSummary** | `string` | Source for country ISO2 code is missing. Target structure has this at the top level of the nested object. `AuthorisedRepresentativeSummary` has `countryName`. |
| `authorisedRepresentatives[].actorStatus.startDate` | | `actorDataPublicView.authorisedRepresentatives[].actorStatusFromDate` | `2025-04-05T13:14:38.173Z` | Map from the `actorStatusFromDate` field. Note: Source is date, target is date-time in example. |
| `authorisedRepresentatives[].actorStatus.endDate` | | **Source Missing in AuthorisedRepresentativeSummary** | `2025-04-05T13:14:38.173Z` | Source for this field is missing. **Doubt** |
| `authorisedRepresentatives[].actorStatus.versionNumber` | | `actorDataPublicView.authorisedRepresentatives[].versionNumber` | `0` | Map from the `versionNumber` field. |
| `authorisedRepresentatives[].actorStatus.versionState` | | `actorDataPublicView.authorisedRepresentatives[].versionState.code` | `string` | Map the `code` field from the nested `versionState` object. |
| `authorisedRepresentatives[].actorStatus.statusFromDate` | | `actorDataPublicView.authorisedRepresentatives[].actorStatusFromDate` | `2025-04-05` | Map from the `actorStatusFromDate` field. |
| `authorisedRepresentatives[].actorStatus.latestVersion` | | `actorDataPublicView.authorisedRepresentatives[].latestVersion` | `true` | Map from the `latestVersion` field. |
| `authorisedRepresentatives[].actorStatus.lastUpdateDate` | | `actorDataPublicView.authorisedRepresentatives[].lastUpdateDate` | `2025-04-05T13:14:38.173Z` | Map from the `lastUpdateDate` field. |
| `authorisedRepresentatives[].actorStatus.status` | | `actorDataPublicView.authorisedRepresentatives[].actorStatus.code` | `string` | Map the `code` field from the nested `actorStatus` object. |
| `authorisedRepresentatives[].names` | | `actorDataPublicView.authorisedRepresentatives[].names` or `actorDataPublicView.authorisedRepresentatives[].abbreviatedNames` | `{...}` (object) | Map the nested `names` or `abbreviatedNames` object. Prioritize `names`. |
| `authorisedRepresentatives[].names.abbreviated` | | | `true` | Indicates whether the record comes from abbreviatedNames (true) or from names (false). |
| `authorisedRepresentatives[].names.language` | | `actorDataPublicView.authorisedRepresentatives[].names.texts[].language.isoCode` or `actorDataPublicView.authorisedRepresentatives[].abbreviatedNames.texts[].language.isoCode` | `string` | Map the `isoCode` from the `language` object within the first `texts` entry. Prioritize `names`. |
| `authorisedRepresentatives[].names.name` | | `actorDataPublicView.authorisedRepresentatives[].names.texts[].text` or `actorDataPublicView.authorisedRepresentatives[].abbreviatedNames.texts[].text` | `string` | Map the `text` from the first `texts` entry. Prioritize `names`. |
| `authorisedRepresentatives[].address.countryIso2Code` | | **Source Missing in AuthorisedRepresentativeSummary** | `string` | Source for country ISO2 code within the nested address is missing. Target structure has this under `address`. `AuthorisedRepresentativeSummary` has `countryName`. |
| `authorisedRepresentatives[].address.countryName` | | `actorDataPublicView.authorisedRepresentatives[].countryName` | `string` | Map from the `countryName` field. Target structure has this under `address`. |
| `authorisedRepresentatives[].address.geographicalAddress` | | `actorDataPublicView.authorisedRepresentatives[].address` | `string` | Map from the `address` field. Target structure has this under `address`. |
| `authorisedRepresentatives[].contact.firstName` | | **Source Missing in AuthorisedRepresentativeSummary** | `string` | Source for contact first name is missing. |
| `authorisedRepresentatives[].contact.familyName` | | **Source Missing in AuthorisedRepresentativeSummary** | `string` | Source for contact family name is missing. |
| `authorisedRepresentatives[].contact.electronicMail` | | `actorDataPublicView.authorisedRepresentatives[].email` | `<EMAIL>` | Map from the `email` field. Target field is `electronicMail`. |
| `authorisedRepresentatives[].contact.telephone` | | `actorDataPublicView.authorisedRepresentatives[].telephone` | `string` | Map from the `telephone` field. |
| `authorisedRepresentatives[].contact.website` | | **Source Missing in AuthorisedRepresentativeSummary** | `string` | Source for contact website is missing. |
| `importers[].uuid` | | `importers[].actor.uuid` | `string` | Map from the nested `uuid` field within the `actor` object. |
| `importers[].srn` | | `importers[].actor.srn` | `string` | Map from the nested `srn` field within the `actor` object. |
| `importers[].actorType` | | `importers[].actor.actorType.code` | `string` | Map the `code` field from the nested `actorType` object within the `actor` object. |
| `importers[].name` | | `importers[].actor.name` | `string` | Map from the nested `name` field within the `actor` object. |
| `importers[].countryIsoCode` | | `importers[].actor.countryIso2Code` | `string` | Map from the nested `countryIso2Code` field within the `actor` object. Target structure has this at the top level of the nested object. |
| `importers[].actorStatus.startDate` | | `importers[].actor.statusFromDate` | `2025-04-05T13:14:38.173Z` | Map from the nested `statusFromDate` field within the `actor` object's status. Note: Source is date, target is date-time in example. |
| `importers[].actorStatus.endDate` | | **Source Missing in ActorSummary** | `2025-04-05T13:14:38.173Z` | Source for this field is missing. **Doubt** |
| `importers[].actorStatus.versionNumber` | | `importers[].actor.versionNumber` | `0` | Map from the nested `versionNumber` field within the `actor` object's status. |
| `importers[].actorStatus.versionState` | | `importers[].actor.versionState.code` | `string` | Map the `code` field from the nested `versionState` object within the `actor` object's status. |
| `importers[].actorStatus.statusFromDate` | | `importers[].actor.statusFromDate` | `2025-04-05` | Map from the nested `statusFromDate` field within the `actor` object's status. |
| `importers[].actorStatus.latestVersion` | | `importers[].actor.latestVersion` | `true` | Map from the nested `latestVersion` field within the `actor` object's status. |
| `importers[].actorStatus.lastUpdateDate` | | `importers[].actor.lastUpdateDate` | `2025-04-05T13:14:38.173Z` | Map from the nested `lastUpdateDate` field within the `actor` object's status. |
| `importers[].actorStatus.status` | | `importers[].actor.status.code` | `string` | Map the `code` field from the nested `status` object within the `actor` object's status. |
| `importers[].names` | | `importers[].actor.names` or `importers[].actor.abbreviatedNames` | `{...}` (object) | Map the nested `names` or `abbreviatedNames` object from within the `actor` object. Prioritize `names`. |
| `importers[].names.abbreviated` | | | `true` | Indicates whether the record comes from abbreviatedNames (true) or from names (false). |
| `importers[].names.language` | | `importers[].actor.names.texts[].language.isoCode` or `importers[].actor.abbreviatedNames.texts[].language.isoCode` | `string` | Map the `isoCode` from the `language` object within the first `texts` entry. Prioritize `names`. |
| `importers[].names.name` | | `importers[].actor.names.texts[].text` or `importers[].actor.abbreviatedNames.texts[].text` | `string` | Map the `text` from the first `texts` entry. Prioritize `names`. |
| `importers[].address.countryIso2Code` | | `importers[].actor.countryIso2Code` | `string` | Map from the nested `countryIso2Code` field within the `actor` object. Target structure has this under `address`. |
| `importers[].address.countryName` | | `importers[].actor.countryName` | `string` | Map from the nested `countryName` field within the `actor` object. Target structure has this under `address`. |
| `importers[].address.geographicalAddress` | | `importers[].actor.geographicalAddress` | `string` | Map from the nested `geographicalAddress` field within the `actor` object. Target structure has this under `address`. |
| `importers[].contact.firstName` | | **Source Missing in ActorSummary** | `string` | Source for contact first name is missing. |
| `importers[].contact.familyName` | | **Source Missing in ActorSummary** | `string` | Source for contact family name is missing. |
| `importers[].contact.electronicMail` | | `importers[].actor.electronicMail` | `<EMAIL>` | Map from the nested `electronicMail` field within the `actor` object. |
| `importers[].contact.telephone` | | `importers[].actor.telephone` | `string` | Map from the nested `telephone` field within the `actor` object. |
| `importers[].contact.website` | | **Source Missing in ActorSummary** | `string` | Source for contact website is missing. |
| `nonEuManufacturers[].uuid` | | `nonEuManufacturers[].uuid` | `string` | Map from the `uuid` field. |
| `nonEuManufacturers[].srn` | | `nonEuManufacturers[].srn` | `string` | Map from the `srn` field. |
| `nonEuManufacturers[].actorType` | | `nonEuManufacturers[].actorType.code` | `string` | Map the `code` field from the nested `actorType` object. |
| `nonEuManufacturers[].name` | | `nonEuManufacturers[].name` | `string` | Map from the `name` field. |
| `nonEuManufacturers[].countryIsoCode` | | `nonEuManufacturers[].countryIso2Code` | `string` | Map from the `countryIso2Code` field. Target structure has this at the top level of the nested object. |
| `nonEuManufacturers[].actorStatus.startDate` | | `nonEuManufacturers[].statusFromDate` | `2025-04-05T13:14:38.173Z` | Map from the `statusFromDate` field. Note: Source is date, target is date-time in example. |
| `nonEuManufacturers[].actorStatus.endDate` | | **Source Missing in ActorSummary** | `2025-04-05T13:14:38.173Z` | Source for this field is missing. **Doubt** |
| `nonEuManufacturers[].actorStatus.versionNumber` | | `nonEuManufacturers[].versionNumber` | `0` | Map from the `versionNumber` field. |
| `nonEuManufacturers[].actorStatus.versionState` | | `nonEuManufacturers[].versionState.code` | `string` | Map the `code` field from the nested `versionState` object. |
| `nonEuManufacturers[].actorStatus.statusFromDate` | | `nonEuManufacturers[].statusFromDate` | `2025-04-05` | Map from the `statusFromDate` field. |
| `nonEuManufacturers[].actorStatus.latestVersion` | | `nonEuManufacturers[].latestVersion` | `true` | Map from the `latestVersion` field. |
| `nonEuManufacturers[].actorStatus.lastUpdateDate` | | `nonEuManufacturers[].lastUpdateDate` | `2025-04-05T13:14:38.173Z` | Map from the `lastUpdateDate` field. |
| `nonEuManufacturers[].actorStatus.status` | | `nonEuManufacturers[].status.code` | `string` | Map the `code` field from the nested `status` object. |
| `nonEuManufacturers[].names` | | `nonEuManufacturers[].names` or `nonEuManufacturers[].abbreviatedNames` | `{...}` (object) | Map the nested `names` or `abbreviatedNames` object. Prioritize `names`. |
| `nonEuManufacturers[].names.abbreviated` | | | `true` | Indicates whether the record comes from abbreviatedNames (true) or from names (false). |
| `nonEuManufacturers[].names.language` | | `nonEuManufacturers[].names.texts[].language.isoCode` or `nonEuManufacturers[].abbreviatedNames.texts[].language.isoCode` | `string` | Map the `isoCode` from the `language` object within the first `texts` entry. Prioritize `names`. |
| `nonEuManufacturers[].names.name` | | `nonEuManufacturers[].names.texts[].text` or `nonEuManufacturers[].abbreviatedNames.texts[].text` | `string` | Map the `text` from the first `texts` entry. Prioritize `names`. |
| `nonEuManufacturers[].address.countryIso2Code` | | `nonEuManufacturers[].countryIso2Code` | `string` | Map from the `countryIso2Code` field. Target structure has this under `address`. |
| `nonEuManufacturers[].address.countryName` | | `nonEuManufacturers[].countryName` | `string` | Map from the `countryName` field. Target structure has this under `address`. |
| `nonEuManufacturers[].address.geographicalAddress` | | `nonEuManufacturers[].geographicalAddress` | `string` | Map from the `geographicalAddress` field. Target structure has this under `address`. |
| `nonEuManufacturers[].contact.firstName` | | **Source Missing in ActorSummary** | `string` | Source for contact first name is missing. |
| `nonEuManufacturers[].contact.familyName` | | **Source Missing in ActorSummary** | `string` | Source for contact family name is missing. |
| `nonEuManufacturers[].contact.electronicMail` | | `nonEuManufacturers[].electronicMail` | `<EMAIL>` | Map from the `electronicMail` field. |
| `nonEuManufacturers[].contact.telephone` | | `nonEuManufacturers[].telephone` | `string` | Map from the `telephone` field. |
| `nonEuManufacturers[].contact.website` | | **Source Missing in ActorSummary** | `string` | Source for contact website is missing. |
| `mandates`  |  | `mandates`  | `[...]` (array of objects)  | Map the array of objects from `getActorDetailsByUuid`. Each object within the array needs further mapping based on the target structure for mandates. |
| `mandateHistory`  |  | `mandateHistory`  | `[...]` (array of objects)  | Map the array of objects from `getActorDetailsByUuid`. Each object within the array needs further mapping based on the target structure for mandate history.  |
| `subcontractors`  |  | `subcontractors`  | `[...]` (array of objects)  | Map the array of objects from `getActorDetailsByUuid`. Each object within the array needs further mapping based on the target structure for subcontractors. |
| `subcontractorHistory`  |  | `subcontractorHistory`  | `[...]` (array of objects)  | Map the array of objects from `getActorDetailsByUuid`. Each object within the array needs further mapping based on the target structure for subcontractor history.  |
| `certificates`  |  | `certificates`  | `[...]` (array of objects)  | Map the array of objects from `getActorDetailsByUuid`. Each object within the array needs further mapping based on the target structure for certificates. |
| `regulatoryComplianceResponsibles`  |  | `regulatoryComplianceResponsibles`  | `[...]` (array of objects)  | Map the array of objects from `getActorDetailsByUuid`. Each object within the array needs further mapping based on the target structure for regulatory compliance responsibles. || `address.countryName` | `content[].countryName` | `actorDataPublicView.actorAddress.country.name` | `Canada`  | Prioritize `searchActors` countryName. If not available, map from `getActorDetailsByUuid`. |
| `address.geographicalAddress` | `content[].geographicalAddress` | `actorDataPublicView.actorAddress.geographicalAddress`  | `3430 Schmon Parkway Thorold` | Prioritize `searchActors` geographicalAddress. If not available, map from `getActorDetailsByUuid`. |
| `address.buildingNumber`  |  | `actorDataPublicView.actorAddress.buildingNumber` | `10`  | Available only in `getActorDetailsByUuid`. |
| `address.streetName`  |  | `actorDataPublicView.actorAddress.streetName` | `Main Street` | Available only in `getActorDetailsByUuid`. |
| `address.postbox` |  | `actorDataPublicView.actorAddress.postbox`  | `P.O. Box 123`  | Available only in `getActorDetailsByUuid`. |
| `address.addressComplement` |  | `actorDataPublicView.actorAddress.complement` | `Suite 400` | Available only in `getActorDetailsByUuid`. The field name is `complement` in the source. |
| `address.postalZone`  |  | `actorDataPublicView.actorAddress.postalZone` | `L2V 4Y6` | Available only in `getActorDetailsByUuid`. |
| `address.cityName`  |  | `actorDataPublicView.actorAddress.cityName` | `Thorold` | Available only in `getActorDetailsByUuid`. |
| `address.gps` |  | `actorDataPublicView.actorAddress.gps`  | `43.111, -79.222` | Available only in `getActorDetailsByUuid`. |
| `contact.firstName` |  | `actorDataPublicView.regulatoryComplianceResponsibles[].firstName` | `John`  | Map from the first entry in `regulatoryComplianceResponsibles` in `getActorDetailsByUuid`. This assumes the first entry is the primary contact. |
| `contact.familyName`  |  | `actorDataPublicView.regulatoryComplianceResponsibles[].familyName` | `Doe` | Map from the first entry in `regulatoryComplianceResponsibles` in `getActorDetailsByUuid`. This assumes the first entry is the primary contact. |
| `contact.electronicMail`  | `content[].electronicMail`  | `actorDataPublicView.electronicMail`  | `<EMAIL>` | Prioritize `searchActors` electronicMail. If not available, map from `getActorDetailsByUuid`.  |
| `contact.telephone` | `content[].telephone` | `actorDataPublicView.telephone` | `9052278848`  | Prioritize `searchActors` telephone. If not available, map from `getActorDetailsByUuid`. |
| `contact.website` |  | `actorDataPublicView.website` | `http://www.norgenbiotek.com` | Available only in `getActorDetailsByUuid`. |
| `authorisedRepresentatives` |  | `authorisedRepresentatives` | `[...]` (array of objects)  | Map the array of objects from `getActorDetailsByUuid`. Each object within the array needs further mapping based on the target structure for authorised representatives. |
| `importers` |  | `importers` | `[...]` (array of objects)  | Map the array of objects from `getActorDetailsByUuid`. Each object within the array needs further mapping based on the target structure for importers. |
| `nonEuManufacturers`  |  | `nonEuManufacturers`  | `[...]` (array of objects)  | Map the array of objects from `getActorDetailsByUuid`. Each object within the array needs further mapping based on the target structure for non-EU manufacturers. |
| `mandates`  |  | `mandates`  | `[...]` (array of objects)  | Map the array of objects from `getActorDetailsByUuid`. Each object within the array needs further mapping based on the target structure for mandates. |
| `mandateHistory`  |  | `mandateHistory`  | `[...]` (array of objects)  | Map the array of objects from `getActorDetailsByUuid`. Each object within the array needs further mapping based on the target structure for mandate history.  |
| `subcontractors`  |  | `subcontractors`  | `[...]` (array of objects)  | Map the array of objects from `getActorDetailsByUuid`. Each object within the array needs further mapping based on the target structure for subcontractors. |
| `subcontractorHistory`  |  | `subcontractorHistory`  | `[...]` (array of objects)  | Map the array of objects from `getActorDetailsByUuid`. Each object within the array needs further mapping based on the target structure for subcontractor history.  |
| `certificates`  |  | `certificates`  | `[...]` (array of objects)  | Map the array of objects from `getActorDetailsByUuid`. Each object within the array needs further mapping based on the target structure for certificates. |
| `regulatoryComplianceResponsibles`  |  | `regulatoryComplianceResponsibles`  | `[...]` (array of objects)  | Map the array of objects from `getActorDetailsByUuid`. Each object within the array needs further mapping based on the target structure for regulatory compliance responsibles. || `address.countryIso2Code` | `content[].countryIso2Code` | `actorDataPublicView.actorAddress.country.iso2Code` | `CA`  | Prioritize `searchActors` countryIso2Code. If not available, map from `getActorDetailsByUuid`. |
| mandates[].uuid |  | `mandates[].uuid` | `string`  | Map from the `uuid` field within each object in the `mandates` array.  |
| mandates[].mandator.uuid  |  | `mandates[].mandator.uuid`  | `string`  | Map from the nested `uuid` field within the `mandator` object in the `mandates` array. |
| mandates[].mandator.srn |  | `mandates[].mandator.srn` | `string`  | Map from the nested `srn` field within the `mandator` object in the `mandates` array.  |
| mandates[].mandator.actorType |  | `mandates[].mandator.actorType.code`  | `string`  | Map the `code` field from the nested `actorType` object within the `mandator` object in the `mandates` array.  |
| mandates[].mandator.name  |  | `mandates[].mandator.name`  | `string`  | Map from the nested `name` field within the `mandator` object in the `mandates` array. |
| mandates[].mandator.countryIso2Code`  |  | `mandates[].mandator.countryIso2Code` | `string`  | Map from the nested `countryIso2Code` field within the `mandator` object in the `mandates` array.  |
| mandates[].mandator.actorStatus.startDate` |  | `mandates[].mandator.statusFromDate`  | `2025-04-05T13:14:38.173Z`  | Map from the nested `statusFromDate` field within the `mandator` object's status in the `mandates` array.  |
| mandates[].mandator.actorStatus.endDate` |  | | `2025-04-05T13:14:38.173Z`  | Source for this field within the nested mandator's actor status is not clear from the schema. **Doubt**  |
| mandates[].mandator.actorStatus.versionNumber` |  | `mandates[].mandator.versionNumber` | `0` | Map from the nested `versionNumber` field within the `mandator` object's status in the `mandates` array. |
| mandates[].mandator.actorStatus.versionState` |  | `mandates[].mandator.versionState.code` | `string`  | Map the `code` field from the nested `versionState` object within the `mandator` object's status in the `mandates` array.  |
| mandates[].mandator.actorStatus.statusFromDate` |  | `mandates[].mandator.statusFromDate`  | `2025-04-05`  | Map from the nested `statusFromDate` field within the `mandator` object's status in the `mandates` array.  |
| mandates[].mandator.actorStatus.latestVersion` |  | `mandates[].mandator.latestVersion` | `true`  | Map from the nested `latestVersion` field within the `mandator` object's status in the `mandates` array. |
| mandates[].mandator.actorStatus.lastUpdateDate` |  | `mandates[].mandator.lastUpdateDate`  | `2025-04-05T13:14:38.173Z`  | Map from the nested `lastUpdateDate` field within the `mandator` object's status in the `mandates` array.  |
| mandates[].mandator.actorStatus.status` | | `mandates[].mandator.status.code` | `string`  | Map the `code` field from the nested `status` object within the `mandator` object's status in the `mandates` array.  |
| mandates[].mandator.names` |  | `mandates[].mandator.names` | `{...}` (object)  | Map the nested `names` object from within the `mandator` object in the `mandates` array. This object needs further mapping (abbreviated, language, name). |
| mandates[].mandator.names.abbreviated` |  | | `true`  | Indicates whether the record comes from abbreviatedNames (true) or from names (false) within the nested names object. **Doubt** |
| mandates[].mandator.names.language`  |  | `mandates[].mandator.names.texts[].language.isoCode`  | `string`  | Map the `isoCode` from the `language` object within the first `texts` entry in the nested `names` object.  |
| mandates[].mandator.names.name`  |  | `mandates[].mandator.names.texts[].text`  | `string`  | Map the `text` from the first `texts` entry in the nested `names` object.  |
| mandates[].mandator.address.countryIso2Code` |  | `mandates[].mandator.countryIso2Code` | `string`  | Map from the nested `countryIso2Code` field within the `mandator` object in the `mandates` array. This seems to be a direct field, not nested under address. **Doubt** |
| mandates[].mandator.address.countryName` |  | `mandates[].mandator.countryName` | `string`  | Map from the nested `countryName` field within the `mandator` object in the `mandates` array. This seems to be a direct field, not nested under address. **Doubt** |
| mandates[].mandator.address.geographicalAddress` | | `mandates[].mandator.geographicalAddress` | `string`  | Map from the nested `geographicalAddress` field within the `mandator` object in the `mandates` array. This seems to be a direct field, not nested under address. **Doubt** |
| mandates[].mandator.contact.firstName` |  | | `string`  | Source for contact details within the nested mandator is not clear from the schema. **Doubt**  |
| mandates[].mandator.contact.familyName` | | | `string`  | See comment for `mandates[].mandator.contact.firstName`. **Doubt** |
| mandates[].mandator.contact.electronicMail` | | `mandates[].mandator.electronicMail`  | `<EMAIL>`  | Map from the nested `electronicMail` field within the `mandator` object in the `mandates` array. |
| mandates[].mandator.contact.telephone` | | `mandates[].mandator.telephone` | `string`  | Map from the nested `telephone` field within the `mandator` object in the `mandates` array.  |
| mandates[].mandator.contact.website` |  | | `string`  | Source for website within the nested mandator is not clear from the schema. **Doubt**  |
| mandates[].mandatee.uuid`  |  | `mandates[].mandatee.uuid`  | `string`  | Map from the nested `uuid` field within the `mandatee` object in the `mandates` array. |
| mandates[].mandatee.srn` |  | `mandates[].mandatee.srn` | `string`  | Map from the nested `srn` field within the `mandatee` object in the `mandates` array.  |
| mandates[].mandatee.actorType` |  | `mandates[].mandatee.actorType.code`  | `string`
| mandates[].mandatee.actorStatus.versionState.code`                       | `string`                                                                                                                                | Map the `code` field from the nested `versionState` object within the `mandatee` object in the `mandates` array.                                                    |
| mandates[].mandatee.actorStatus.statusFromDate` |                                        | `mandates[].mandatee.statusFromDate`                          | `2025-04-05`                                                                                                                            | Map from the nested `statusFromDate` field within the `mandatee` object's status in the `mandates` array.                                                                    |
| mandates[].mandatee.actorStatus.latestVersion` |                                        | `mandates[].mandatee.latestVersion`                           | `true`                                                                                                                                  | Map from the nested `latestVersion` field within the `mandatee` object's status in the `mandates` array.                                                                     |
| mandates[].mandatee.actorStatus.lastUpdateDate` |                                        | `mandates[].mandatee.lastUpdateDate`                          | `2025-04-05T13:14:38.173Z`                                                                                                              | Map from the nested `lastUpdateDate` field within the `mandatee` object's status in the `mandates` array.                                                                    |
| mandates[].mandatee.actorStatus.status` |                                                 | `mandates[].mandatee.status.code`                             | `string`                                                                                                                                | Map the `code` field from the nested `status` object within the `mandatee` object's status in the `mandates` array.                                                          |
| mandates[].mandatee.names`             |                                                      | `mandates[].mandatee.names`                                   | `{...}` (object)                                                                                                                        | Map the nested `names` object from within the `mandatee` object in the `mandates` array. This object needs further mapping (abbreviated, language, name). |
| mandates[].mandatee.names.abbreviated` |                                                  |                                                               | `true`                                                                                                                                  | Indicates whether the record comes from abbreviatedNames (true) or from names (false) within the nested names object. **Doubt**                                             |
| mandates[].mandatee.names.language`    |                                                      | `mandates[].mandatee.names.texts[].language.isoCode`          | `string`                                                                                                                                | Map the `isoCode` from the `language` object within the first `texts` entry in the nested `names` object.                                                                    |
| mandates[].mandatee.names.name`        |                                                      | `mandates[].mandatee.names.texts[].text`                      | `string`                                                                                                                                | Map the `text` from the first `texts` entry in the nested `names` object.                                                                                                    |
| mandates[].mandatee.address.countryIso2Code` |                                            | `mandates[].mandatee.countryIso2Code`                         | `string`                                                                                                                                | Map from the nested `countryIso2Code` field within the `mandatee` object in the `mandates` array. This seems to be a direct field, not nested under address. **Doubt** |
| mandates[].mandatee.address.countryName` |                                              | `mandates[].mandatee.countryName`                             | `string`                                                                                                                                | Map from the nested `countryName` field within the `mandatee` object in the `mandates` array. This seems to be a direct field, not nested under address. **Doubt** |
| mandates[].mandatee.address.geographicalAddress` |                                       | `mandates[].mandatee.geographicalAddress`                     | `string`                                                                                                                                | Map from the nested `geographicalAddress` field within the `mandatee` object in the `mandates` array. This seems to be a direct field, not nested under address. **Doubt** |
| mandates[].mandatee.contact.firstName` |                                                |                                                               | `string`                                                                                                                                | Source for contact details within the nested mandatee is not clear from the schema. **Doubt**                                                                          |
| mandates[].mandatee.contact.familyName` |                                               |                                                               | `string`                                                                                                                                | See comment for `mandates[].mandatee.contact.firstName`. **Doubt**                                                                                                   |
| mandates[].mandatee.contact.electronicMail` |                                             | `mandates[].mandatee.electronicMail`                          | `<EMAIL>`                                                                                                                      | Map from the nested `electronicMail` field within the `mandatee` object in the `mandates` array.                                                                           |
| mandates[].mandatee.contact.telephone` |                                               | `mandates[].mandatee.telephone`                               | `string`                                                                                                                                | Map from the nested `telephone` field within the `mandatee` object in the `mandates` array.                                                                              |
| mandates[].mandatee.contact.website`   |                                                |                                                               | `string`                                                                                                                                | Source for website within the nested mandatee is not clear from the schema. **Doubt**                                                                                  |
| mandateHistory`                         |                                                      | `mandateHistory`                                              | `[...]` (array of objects)                                                                                                              | Map the array of objects from `getActorDetailsByUuid`. Detailed mapping for objects within this array follows.                                                              |
| mandateHistory[].uuid`                  |                                                      | `mandateHistory[].uuid`                                       | `string`                                                                                                                                | Map from the `uuid` field within each object in the `mandateHistory` array.                                                                                                  |
| mandateHistory[].mandator.uuid`         |                                                      | `mandateHistory[].mandator.uuid`                              | `string`                                                                                                                                | Map from the nested `uuid` field within the `mandator` object in the `mandateHistory` array.                                                                                 |
| mandateHistory[].mandator.srn`          |                                                      | `mandateHistory[].mandator.srn`                               | `string`                                                                                                                                | Map from the nested `srn` field within the `mandator` object in the `mandateHistory` array.                                                                                  |
| mandateHistory[].mandator.actorType`    |                                                      | `mandateHistory[].mandator.actorType.code`                   | `string`                                                                                                                                | Map the `code` field from the nested `actorType` object within the `mandator` object in the `mandateHistory` array.                                                        |
| mandateHistory[].mandator.name`         |                                                      | `mandateHistory[].mandator.name`                              | `string`                                                                                                                                | Map from the nested `name` field within the `mandator` object in the `mandateHistory` array.                                                                                 |
| mandateHistory[].mandator.countryIso2Code` |                                               | `mandateHistory[].mandator.countryIso2Code`                  | `string`                                                                                                                                | Map from the nested `countryIso2Code` field within the `mandator` object in the `mandateHistory` array.                                                                    |
| mandateHistory[].mandator.actorStatus.startDate` |                                         | `mandateHistory[].mandator.statusFromDate`                   | `2025-04-05T13:14:38.173Z`                                                                                                              | Map from the nested `statusFromDate` field within the `mandator` object's status in the `mandateHistory` array.                                                              |
| mandateHistory[].mandator.actorStatus.endDate` |                                           |                                                               | `2025-04-05T13:14:38.173Z`                                                                                                              | Source for this field within the nested mandator's actor status is not clear from the schema. **Doubt**                                                                      |
| mandateHistory[].mandator.actorStatus.versionNumber` |                                     | `mandateHistory[].mandator.versionNumber`                    | `0`                                                                                                                                     | Map from the nested `versionNumber` field within the `mandator` object's status in the `mandateHistory` array.                                                               |
| mandateHistory[].mandator.actorStatus.versionState` |                                     | `mandateHistory[].mandator.versionState.code`                | `string`                                                                                                                                | Map the `code` field from the nested `versionState` object within the `mandator` object's status in the `mandateHistory` array.                                             |
| mandateHistory[].mandator.actorStatus.statusFromDate` |                                   | `mandateHistory[].mandator.statusFromDate`                   | `2025-04-05`                                                                                                                            | Map from the nested `statusFromDate` field within the `mandator` object's status in the `mandateHistory` array.                                                              |
| mandateHistory[].mandator.actorStatus.latestVersion` |                                   | `mandateHistory[].mandator.latestVersion`                    | `true`                                                                                                                                  | Map from the nested `latestVersion` field within the `mandator` object's status in the `mandateHistory` array.                                                               |
| mandateHistory[].mandator.actorStatus.lastUpdateDate` |                                   | `mandateHistory[].mandator.lastUpdateDate`                   | `2025-04-05T13:14:38.173Z`                                                                                                              | Map from the nested `lastUpdateDate` field within the `mandator` object's status in the `mandateHistory` array.                                                              |
| mandateHistory[].mandator.actorStatus.status` |                                            | `mandateHistory[].mandator.status.code`                      | `string`                                                                                                                                | Map the `code` field from the nested `status` object within the `mandator` object's status in the `mandateHistory` array.                                                   |
| mandateHistory[].mandator.names`        |                                                      | `mandateHistory[].mandator.names`                             | `{...}` (object)                                                                                                                        | Map the nested `names` object from within the `mandator` object in the `mandateHistory` array. This object needs further mapping (abbreviated, language, name). |
| mandateHistory[].mandator.names.abbreviated` |                                             |                                                               | `true`                                                                                                                                  | Indicates whether the record comes from abbreviatedNames (true) or from names (false) within the nested names object. **Doubt**                                             |
| mandateHistory[].mandator.names.language` |                                             | `mandateHistory[].mandator.names.texts[].language.isoCode`   | `string`                                                                                                                                | Map the `isoCode` from the `language` object within the first `texts` entry in the nested `names` object.                                                                    |
| mandateHistory[].mandator.names.name`   |                                                      | `mandateHistory[].mandator.names.texts[].text`               | `string`                                                                                                                                | Map the `text` from the first `texts` entry in the nested `names` object.                                                                                                    |
| mandateHistory[].mandator.address.countryIso2Code` |                                      | `mandateHistory[].mandator.countryIso2Code`                  | `string`                                                                                                                                | Map from the nested `countryIso2Code` field within the `mandator` object in the `mandateHistory` array. This seems to be a direct field, not nested under address. **Doubt** |
| mandateHistory[].mandator.address.countryName` |                                        | `mandateHistory[].mandator.countryName`                      | `string`                                                                                                                                | Map from the nested `countryName` field within the `mandator` object in the `mandateHistory` array. This seems to be a direct field, not nested under address. **Doubt** |
| mandateHistory[].mandator.address.geographicalAddress` |                                | `mandateHistory[].mandator.geographicalAddress`              | `string`                                                                                                                                | Map from the nested `geographicalAddress` field within the `mandator` object in the `mandateHistory` array. This seems to be a direct field, not nested under address. **Doubt** |
| mandateHistory[].mandator.contact.firstName` |                                          |                                                               | `string`                                                                                                                                | Source for contact details within the nested mandator is not clear from the schema. **Doubt**                                                                          |
| mandateHistory[].mandator.contact.familyName` |                                         |                                                               | `string`                                                                                                                                | See comment for `mandateHistory[].mandator.contact.firstName`. **Doubt**                                                                                                  |
| mandateHistory[].mandator.contact.electronicMail` |                                      | `mandateHistory[].mandator.electronicMail`                   | `<EMAIL>`                                                                                                                      | Map from the nested `electronicMail` field within the `mandator` object in the `mandateHistory` array.                                                                           |
| mandateHistory[].mandator.contact.telephone` |                                          | `mandateHistory[].mandator.telephone`                        | `string`                                                                                                                                | Map from the nested `telephone` field within the `mandator` object in the `mandateHistory` array.                                                                              |
| mandateHistory[].mandator.contact.website` |                                            |                                                               | `string`                                                                                                                                | Source for website within the nested mandator is not clear from the schema. **Doubt**                                                                                  |
| mandateHistory[].mandatee.uuid`         |                                                      | `mandateHistory[].mandatee.uuid`                              | `string`                                                                                                                                | Map from the nested `uuid` field within the `mandatee` object in the `mandateHistory` array.                                                                                 |
| mandateHistory[].mandatee.srn`          |                                                      | `mandateHistory[].mandatee.srn`                               | `string`                                                                                                                                | Map from the nested `srn` field within the `mandatee` object in the `mandateHistory` array.                                                                                  |
| mandateHistory[].mandatee.actorType`    |                                                      | `mandateHistory[].mandatee.actorType.code`                   | `string`                                                                                                                                | Map the `code` field from the nested `actorType` object within the `mandatee` object in the `mandateHistory` array.                                                        |
| mandateHistory[].mandatee.name`         |                                                      | `mandateHistory[].mandatee.name`                              | `string`                                                                                                                                | Map from the nested `name` field within the `mandatee` object in the `mandateHistory` array.                                                                                 |
| mandateHistory[].mandatee.countryIso2Code` |                                               | `mandateHistory[].mandatee.countryIso2Code`                  | `string`                                                                                                                                | Map from the nested `countryIso2Code` field within the `mandatee` object in the `mandateHistory` array.                                                                    |
| mandateHistory[].mandatee.actorStatus.startDate` |                                         | `mandateHistory[].mandatee.statusFromDate`                   | `2025-04-05T13:14:38.173Z`                                                                                                              | Map from the nested `statusFromDate` field within the `mandatee` object's status in the `mandateHistory` array.                                                              |
| mandateHistory[].mandatee.actorStatus.endDate` |                                           |                                                               | `2025-04-05T13:14:38.173Z`                                                                                                              | Source for this field within the nested mandatee's actor status is not clear from the schema. **Doubt**                                                                      |
| mandateHistory[].mandatee.actorStatus.versionNumber` |                                     | `mandateHistory[].mandatee.versionNumber`                    | `0`                                                                                                                                     | Map from the nested `versionNumber` field within the `mandatee` object's status in the `mandateHistory` array.                                                               |
| mandateHistory[].mandatee.actorStatus.versionState` |                                     | `mandateHistory[].mandatee.versionState.code`                | `string`                                                                                                                                | Map the `code` field from the nested `versionState` object within the `mandatee` object's status in the `mandateHistory` array.                                             |
| mandateHistory[].mandatee.actorStatus.statusFromDate` |                                   | `mandateHistory[].mandatee.statusFromDate`                   | `2025-04-05`                                                                                                                            | Map from the nested `statusFromDate` field within the `mandatee` object's status in the `mandateHistory` array.                                                              |
| mandateHistory[].mandatee.actorStatus.latestVersion` |                                   | `mandateHistory[].mandatee.latestVersion`                    | `true`                                                                                                                                  | Map from the nested `latestVersion` field within the `mandatee` object's status in the `mandateHistory` array.                                                               |
| mandateHistory[].mandatee.actorStatus.lastUpdateDate` |                                   | `mandateHistory[].mandatee.lastUpdateDate`                   | `2025-04-05T13:14:38.173Z`                                                                                                              | Map from the nested `lastUpdateDate` field within the `mandatee` object's status in the `mandateHistory` array.                                                              |
| mandateHistory[].mandatee.actorStatus.status` |                                            | `mandateHistory[].mandatee.status.code`                      | `string`                                                                                                                                | Map the `code` field from the nested `status` object within the `mandatee` object's status in the `mandateHistory` array.                                                   |
| mandateHistory[].mandatee.names`        |                                                      | `mandateHistory[].mandatee.names`                             | `{...}` (object)                                                                                                                        | Map the nested `names` object from within the `mandatee` object in the `mandateHistory` array. This object needs further mapping (abbreviated, language, name). |
| mandateHistory[].mandatee.names.abbreviated` |                                             |                                                               | `true`                                                                                                                                  | Indicates whether the record comes from abbreviatedNames (true) or from names (false) within the nested names object. **Doubt**                                             |
| mandateHistory[].mandatee.names.language` |                                             | `mandateHistory[].mandatee.names.texts[].language.isoCode`   | `string`                                                                                                                                | Map the `isoCode` from the `language` object within the first `texts` entry in the nested `names` object.                                                                    |
| mandateHistory[].mandatee.names.name`   |                                                      | `mandateHistory[].mandatee.names.texts[].text`               | `string`                                                                                                                                | Map the `text` from the first `texts` entry in the nested `names` object.                                                                                                    |
| mandateHistory[].mandatee.address.countryIso2Code` |                                      | `mandateHistory[].mandatee.countryIso2Code`                  | `string`                                                                                                                                | Map from the nested `countryIso2Code` field within the `mandatee` object in the `mandateHistory` array. This seems to be a direct field, not nested under address. **Doubt** |
| mandateHistory[].mandatee.address.countryName` |                                        | `mandateHistory[].mandatee.countryName`                      | `string`                                                                                                                                | Map from the nested `countryName` field within the `mandatee` object in the `mandateHistory` array. This seems to be a direct field, not nested under address. **Doubt** |
| mandateHistory[].mandatee.address.geographicalAddress` |                                | `mandateHistory[].mandatee.geographicalAddress`              | `string`                                                                                                                                | Map from the nested `geographicalAddress` field within the `mandatee` object in the `mandateHistory` array. This seems to be a direct field, not nested under address. **Doubt** |
| mandateHistory[].mandatee.contact.firstName` |                                          |                                                               | `string`                                                                                                                                | Source for contact details within the nested mandatee is not clear from the schema. **Doubt**                                                                          |
| mandateHistory[].mandatee.contact.familyName` |                                         |                                                               | `string`                                                                                                                                | See comment for `mandateHistory[].mandatee.contact.firstName`. **Doubt**                                                                                                  |
| mandateHistory[].mandatee.contact.electronicMail` |                                      | `mandateHistory[].mandatee.electronicMail`                   | `<EMAIL>`                                                                                                                      | Map from the nested `electronicMail` field within the `mandatee` object in the `mandateHistory` array.                                                                           |
| mandateHistory[].mandatee.contact.telephone` |                                          | `mandateHistory[].mandatee.telephone`                        | `string`                                                                                                                                | Map from the nested `telephone` field within the `mandatee` object in the `mandateHistory` array.                                                                              |
| mandateHistory[].mandatee.contact.website` |                                            |                                                               | `string`                                                                                                                                | Source for website within the nested mandatee is not clear from the schema. **Doubt**                                                                                  |
| subcontractors`                         |                                                      | `subcontractors`                                              | `[...]` (array of objects)                                                                                                              | Map the array of objects from `getActorDetailsByUuid`. Detailed mapping for objects within this array follows.                                                              |
| subcontractors[].subcontractor.contact.familyName` |                                     |                                                               | `string`                                                                                                                                | See comment for `subcontractors[].subcontractor.contact.firstName`. **Doubt**                                                                                                  |
| subcontractors[].subcontractor.contact.electronicMail` |                                   | `subcontractors[].subcontractor.electronicMail`               | `<EMAIL>`                                                                                                                      | Map from the nested `electronicMail` field within the `subcontractor` object in the `subcontractors` array.                                                                           |
| subcontractors[].subcontractor.contact.telephone` |                                     | `subcontractors[].subcontractor.telephone`                     | `string`                                                                                                                                | Map from the nested `telephone` field within the `subcontractor` object in the `subcontractors` array.                                                                              |
| subcontractors[].subcontractor.contact.website` |                                       |                                                               | `string`                                                                                                                                | Source for website within the nested subcontractor is not clear from the schema. **Doubt**                                                                       |
| subcontractorHistory`                   |                                                      | `subcontractorHistory`                                        | `[...]` (array of objects)                                                                                                              | Map the array of objects from `getActorDetailsByUuid`. Detailed mapping for objects within this array follows.                                                              |
| subcontractorHistory[].uuid`            |                                                      | `subcontractorHistory[].uuid`                                 | `string`                                                                                                                                | Map from the `uuid` field within each object in the `subcontractorHistory` array.                                                                                              |
| subcontractorHistory[].contractor.uuid` |                                                      | `subcontractorHistory[].contractor.uuid`                      | `string`                                                                                                                                | Map from the nested `uuid` field within the `contractor` object in the `subcontractorHistory` array.                                                                         |
| subcontractorHistory[].contractor.srn`  |                                                      | `subcontractorHistory[].contractor.srn`                       | `string`                                                                                                                                | Map from the nested `srn` field within the `contractor` object in the `subcontractorHistory` array.                                                                            |
| subcontractorHistory[].contractor.actorType` |                                               | `subcontractorHistory[].contractor.actorType.code`            | `string`                                                                                                                                | Map the `code` field from the nested `actorType` object within the `contractor` object in the `subcontractorHistory` array.                                                        |
| subcontractorHistory[].contractor.name` |                                                      | `subcontractorHistory[].contractor.name`                      | `string`                                                                                                                                | Map from the nested `name` field within the `contractor` object in the `subcontractorHistory` array.                                                                         |
| subcontractorHistory[].contractor.countryIso2Code` |                                        | `subcontractorHistory[].contractor.countryIso2Code`           | `string`                                                                                                                                | Map from the nested `countryIso2Code` field within the `contractor` object in the `subcontractorHistory` array.                                                              |
| subcontractorHistory[].contractor.actorStatus.startDate` |                                  | `subcontractorHistory[].contractor.statusFromDate`            | `2025-04-05T13:14:38.173Z`                                                                                                              | Map from the nested `statusFromDate` field within the `contractor` object's status in the `subcontractorHistory` array.                                                              |
| subcontractorHistory[].contractor.actorStatus.endDate` |                                    |                                                               | `2025-04-05T13:14:38.173Z`                                                                                                              | Source for this field within the nested contractor's actor status is not clear from the schema. **Doubt**                                                                      |
| subcontractorHistory[].contractor.actorStatus.versionNumber` |                                | `subcontractorHistory[].contractor.versionNumber`             | `0`                                                                                                                                     | Map from the nested `versionNumber` field within the `contractor` object's status in the `subcontractorHistory` array.                                                               |
| subcontractorHistory[].contractor.actorStatus.versionState` |                                | `subcontractorHistory[].contractor.versionState.code`         | `string`                                                                                                                                | Map the `code` field from the nested `versionState` object within the `contractor` object's status in the `subcontractorHistory` array.                                             |
| subcontractorHistory[].contractor.actorStatus.statusFromDate` |                               | `subcontractorHistory[].contractor.statusFromDate`            | `2025-04-05`                                                                                                                            | Map from the nested `statusFromDate` field within the `contractor` object's status in the `subcontractorHistory` array.                                                              |
| subcontractorHistory[].contractor.actorStatus.latestVersion` |                               | `subcontractorHistory[].contractor.latestVersion`             | `true`                                                                                                                                  | Map from the nested `latestVersion` field within the `contractor` object's status in the `subcontractorHistory` array.                                                               |
| subcontractorHistory[].contractor.actorStatus.lastUpdateDate` |                               | `subcontractorHistory[].contractor.lastUpdateDate`            | `2025-04-05T13:14:38.173Z`                                                                                                              | Map from the nested `lastUpdateDate` field within the `contractor` object's status in the `subcontractorHistory` array.                                                              |
| subcontractorHistory[].contractor.actorStatus.status` |                                     | `subcontractorHistory[].contractor.status.code`               | `string`                                                                                                                                | Map the `code` field from the nested `status` object within the `contractor` object's status in the `subcontractorHistory` array.                                                  |
| subcontractorHistory[].contractor.names` |                                                      | `subcontractorHistory[].contractor.names`                    | `{...}` (object)                                                                                                                        | Map the nested `names` object from within the `contractor` object in the `subcontractorHistory` array. This object needs further mapping (abbreviated, language, name). |
| subcontractorHistory[].contractor.names.abbreviated` |                                       |                                                               | `true`                                                                                                                                  | Indicates whether the record comes from abbreviatedNames (true) or from names (false) within the nested names object. **Doubt**                                             |
| subcontractorHistory[].contractor.names.language` |                                       | `subcontractorHistory[].contractor.names.texts[].language.isoCode` | `string`                                                                                                                                | Map the `isoCode` from the `language` object within the first `texts` entry in the nested `names` object.                                                                    |
| subcontractorHistory[].contractor.names.name` |                                             | `subcontractorHistory[].contractor.names.texts[].text`        | `string`                                                                                                                                | Map the `text` from the first `texts` entry in the nested `names` object.                                                                                                    |
| subcontractorHistory[].contractor.address.countryIso2Code` |                                 | `subcontractorHistory[].contractor.countryIso2Code`           | `string`                                                                                                                                | Map from the nested `countryIso2Code` field within the `contractor` object in the `subcontractorHistory` array. This seems to be a direct field, not nested under address. **Doubt** |
| subcontractorHistory[].contractor.address.countryName` |                                   | `subcontractorHistory[].contractor.countryName`               | `string`                                                                                                                                | Map from the nested `countryName` field within the `contractor` object in the `subcontractorHistory` array. This seems to be a direct field, not nested under address. **Doubt** |
| subcontractorHistory[].contractor.address.geographicalAddress` |                             | `subcontractorHistory[].contractor.geographicalAddress`       | `string`                                                                                                                                | Map from the nested `geographicalAddress` field within the `contractor` object in the `subcontractorHistory` array. This seems to be a direct field, not nested under address. **Doubt** |
| subcontractorHistory[].contractor.contact.firstName` |                                     |                                                               | `string`                                                                                                                                | Source for contact details within the nested contractor is not clear from the schema. **Doubt**                                                                          |
| subcontractorHistory[].contractor.contact.familyName` |                                    |                                                               | `string`                                                                                                                                | See comment for `subcontractorHistory[].contractor.contact.firstName`. **Doubt**                                                                                                  |
| subcontractorHistory[].contractor.contact.electronicMail` |                                | `subcontractorHistory[].contractor.electronicMail`            | `<EMAIL>`                                                                                                                      | Map from the nested `electronicMail` field within the `contractor` object in the `subcontractorHistory` array.                                                                           |
| subcontractorHistory[].contractor.contact.telephone` |                                    | `subcontractorHistory[].contractor.telephone`                 | `string`                                                                                                                                | Map from the nested `telephone` field within the `contractor` object in the `subcontractorHistory` array.                                                                              |
| subcontractorHistory[].contractor.contact.website` |                                      |                                                               | `string`                                                                                                                                | Source for website within the nested contractor is not clear from the schema. **Doubt**                                                                                  |
| subcontractorHistory[].subcontractor.uuid` |                                                      | `subcontractorHistory[].subcontractor.uuid`                   | `string`                                                                                                                                | Map from the nested `uuid` field within the `subcontractor` object in the `subcontractorHistory` array.                                                                            |
| subcontractorHistory[].subcontractor.srn` |                                                      | `subcontractorHistory[].subcontractor.srn`                    | `string`                                                                                                                                | Map from the nested `srn` field within the `subcontractor` object in the `subcontractorHistory` array.                                                                             |
| subcontractorHistory[].subcontractor.actorType` |                                                | `subcontractorHistory[].subcontractor.actorType.code`         | `string`                                                                                                                                | Map the `code` field from the nested `actorType` object within the `subcontractor` object in the `subcontractorHistory` array.                                                     |
| subcontractorHistory[].subcontractor.name` |                                                      | `subcontractorHistory[].subcontractor.name`                   | `string`                                                                                                                                | Map from the nested `name` field within the `subcontractor` object in the `subcontractorHistory` array.                                                                            |
| subcontractorHistory[].subcontractor.countryIso2Code` |                                      | `subcontractorHistory[].subcontractor.countryIso2Code`        | `string`                                                                                                                                | Map from the nested `countryIso2Code` field within the `subcontractor` object in the `subcontractorHistory` array. This seems to be a direct field, not nested under address. **Doubt** |
| subcontractorHistory[].subcontractor.countryName` |                                    | `subcontractorHistory[].subcontractor.countryName`            | `string`                                                                                                                                | Map from the nested `countryName` field within the `subcontractor` object in the `subcontractorHistory` array. This seems to be a direct field, not nested under address. **Doubt** |
| subcontractorHistory[].subcontractor.address.geographicalAddress` |                             | `subcontractorHistory[].subcontractor.geographicalAddress`    | `string`                                                                                                                                | Map from the nested `geographicalAddress` field within the `subcontractor` object in the `subcontractorHistory` array. This seems to be a direct field, not nested under address. **Doubt** |
| subcontractorHistory[].subcontractor.contact.firstName` |                                  |                                                               | `string`                                                                                                                                | Source for contact details within the nested subcontractor is not clear from the schema. **Doubt**                                                                       |
| subcontractorHistory[].subcontractor.contact.familyName` |                                 |                                                               | `string`                                                                                                                                | See comment for `subcontractorHistory[].subcontractor.contact.firstName`. **Doubt**                                                                                                  |
| subcontractorHistory[].subcontractor.contact.electronicMail` |                               | `subcontractorHistory[].subcontractor.electronicMail`         | `<EMAIL>`                                                                                                                      | Map from the nested `electronicMail` field within the `subcontractor` object in the `subcontractorHistory` array.                                                                           |
| subcontractorHistory[].subcontractor.contact.telephone` |                                 | `subcontractorHistory[].subcontractor.telephone`              | `string`                                                                                                                                | Map from the nested `telephone` field within the `subcontractor` object in the `subcontractorHistory` array.
| subcontractors[].uuid`                  |                                                      | `subcontractors[].uuid`                                      | `string`                                                                                                                                | Map from the `uuid` field within each object in the `subcontractors` array.                                                                                                  |
| subcontractors[].contractor.uuid`       |                                                      | `subcontractors[].contractor.uuid`                            | `string`                                                                                                                                | Map from the nested `uuid` field within the `contractor` object in the `subcontractors` array.                                                                               |
| subcontractors[].contractor.srn`        |                                                      | `subcontractors[].contractor.srn`                             | `string`                                                                                                                                | Map from the nested `srn` field within the `contractor` object in the `subcontractors` array.                                                                                |
| subcontractors[].contractor.actorType`   |                                                      | `subcontractors[].contractor.actorType.code`                 | `string`                                                                                                                                | Map the `code` field from the nested `actorType` object within the `contractor` object in the `subcontractors` array.                                                        |
| subcontractors[].contractor.name`       |                                                      | `subcontractors[].contractor.name`                            | `string`                                                                                                                                | Map from the nested `name` field within the `contractor` object in the `subcontractors` array.                                                                               |
| subcontractors[].contractor.countryIso2Code` |                                            | `subcontractors[].contractor.countryIso2Code`                 | `string`                                                                                                                                | Map from the nested `countryIso2Code` field within the `contractor` object in the `subcontractors` array.                                                                    |
| subcontractors[].contractor.actorStatus.startDate` |                                      | `subcontractors[].contractor.statusFromDate`                  | `2025-04-05T13:14:38.173Z`                                                                                                              | Map from the nested `statusFromDate` field within the `contractor` object's status in the `subcontractors` array.                                                              |
| subcontractors[].contractor.actorStatus.endDate` |                                        |                                                               | `2025-04-05T13:14:38.173Z`                                                                                                              | Source for this field within the nested contractor's actor status is not clear from the schema. **Doubt**                                                                      |
| subcontractors[].contractor.actorStatus.versionNumber` |                                  | `subcontractors[].contractor.versionNumber`                   | `0`                                                                                                                                     | Map from the nested `versionNumber` field within the `contractor` object's status in the `subcontractors` array.                                                               |
| subcontractors[].contractor.actorStatus.versionState` |                                  | `subcontractors[].contractor.versionState.code`              | `string`                                                                                                                                | Map the `code` field from the nested `versionState` object within the `contractor` object's status in the `subcontractors` array.                                             |
| subcontractors[].contractor.actorStatus.statusFromDate` |                                | `subcontractors[].contractor.statusFromDate`                  | `2025-04-05`                                                                                                                            | Map from the nested `statusFromDate` field within the `contractor` object's status in the `subcontractors` array.                                                              |
| subcontractors[].contractor.actorStatus.latestVersion` |                                | `subcontractors[].contractor.latestVersion`                   | `true`                                                                                                                                  | Map from the nested `latestVersion` field within the `contractor` object's status in the `subcontractors` array.                                                               |
| subcontractors[].contractor.actorStatus.lastUpdateDate` |                                | `subcontractors[].contractor.lastUpdateDate`                  | `2025-04-05T13:14:38.173Z`                                                                                                              | Map from the nested `lastUpdateDate` field within the `contractor` object's status in the `subcontractors` array.                                                              |
| subcontractors[].contractor.actorStatus.status` |                                         | `subcontractors[].contractor.status.code`                    | `string`                                                                                                                                | Map the `code` field from the nested `status` object within the `contractor` object's status in the `subcontractors` array.                                                  |
| subcontractors[].contractor.names`      |                                                      | `subcontractors[].contractor.names`                           | `{...}` (object)                                                                                                                        | Map the nested `names` object from within the `contractor` object in the `subcontractors` array. This object needs further mapping (abbreviated, language, name). |
| subcontractors[].contractor.names.abbreviated` |                                          |                                                               | `true`                                                                                                                                  | Indicates whether the record comes from abbreviatedNames (true) or from names (false) within the nested names object. **Doubt**                                             |
| subcontractors[].contractor.names.language` |                                          | `subcontractors[].contractor.names.texts[].language.isoCode` | `string`                                                                                                                                | Map the `isoCode` from the `language` object within the first `texts` entry in the nested `names` object.                                                                    |
| subcontractors[].contractor.names.name` |                                                      | `subcontractors[].contractor.names.texts[].text`              | `string`                                                                                                                                | Map the `text` from the first `texts` entry in the nested `names` object.                                                                                                    |
| subcontractors[].contractor.address.countryIso2Code` |                                    | `subcontractors[].contractor.countryIso2Code`                 | `string`                                                                                                                                | Map from the nested `countryIso2Code` field within the `contractor` object in the `subcontractors` array. This seems to be a direct field, not nested under address. **Doubt** |
| subcontractors[].contractor.address.countryName` |                                      | `subcontractors[].contractor.countryName`                     | `string`                                                                                                                                | Map from the nested `countryName` field within the `contractor` object in the `subcontractors` array. This seems to be a direct field, not nested under address. **Doubt** |
| subcontractors[].contractor.address.geographicalAddress` |                                 | `subcontractors[].contractor.geographicalAddress`             | `string`                                                                                                                                | Map from the nested `geographicalAddress` field within the `contractor` object in the `subcontractors` array. This seems to be a direct field, not nested under address. **Doubt** |
| subcontractors[].contractor.contact.firstName` |                                        |                                                               | `string`                                                                                                                                | Source for contact details within the nested contractor is not clear from the schema. **Doubt**                                                                          |
| subcontractors[].contractor.contact.familyName` |                                       |                                                               | `string`                                                                                                                                | See comment for `subcontractors[].contractor.contact.firstName`. **Doubt**                                                                                                  |
| subcontractors[].contractor.contact.electronicMail` |                                     | `subcontractors[].contractor.electronicMail`                  | `<EMAIL>`                                                                                                                      | Map from the nested `electronicMail` field within the `contractor` object in the `subcontractors` array.                                                                           |
| subcontractors[].contractor.contact.telephone` |                                       | `subcontractors[].contractor.telephone`                       | `string`                                                                                                                                | Map from the nested `telephone` field within the `contractor` object in the `subcontractors` array.                                                                              |
| subcontractors[].contractor.contact.website` |                                         |                                                               | `string`                                                                                                                                | Source for website within the nested contractor is not clear from the schema. **Doubt**                                                                                  |
| subcontractors[].subcontractor.uuid`    |                                                      | `subcontractors[].subcontractor.uuid`                         | `string`                                                                                                                                | Map from the nested `uuid` field within the `subcontractor` object in the `subcontractors` array.                                                                            |
| subcontractors[].subcontractor.srn`     |                                                      | `subcontractors[].subcontractor.srn`                          | `string`                                                                                                                                | Map from the nested `srn` field within the `subcontractor` object in the `subcontractors` array.                                                                             |
| subcontractorHistory[].subcontractor.contact.familyName` |                                 |                                                               | `string`                                                                                                                                | See comment for `subcontractorHistory[].subcontractor.contact.firstName`. **Doubt**                                                                                                  |
| subcontractorHistory[].subcontractor.contact.electronicMail` |                               | `subcontractorHistory[].subcontractor.electronicMail`         | `<EMAIL>`                                                                                                                      | Map from the nested `electronicMail` field within the `subcontractor` object in the `subcontractorHistory` array.                                                                           |
| subcontractorHistory[].subcontractor.contact.telephone` |                                 | `subcontractorHistory[].subcontractor.telephone`              | `string`                                                                                                                                | Map from the nested `telephone` field within the `subcontractor` object in the `subcontractorHistory` array.                                                                              |
| subcontractorHistory[].subcontractor.contact.website` |                                   |                                                               | `string`                                                                                                                                | Source for website within the nested subcontractor is not clear from the schema. **Doubt**                                                                       |
| certificates`                           |                                                      | `certificates`                                                | `[...]` (array of objects)                                                                                                              | Map the array of objects from `getActorDetailsByUuid`. Detailed mapping for objects within this array follows.                                                              |
| certificates[].uuid`                    |                                                      | `certificates[].uuid`                                         | `string`                                                                                                                                | Map from the `uuid` field within each object in the `certificates` array.                                                                                                  |
| certificates[].certificateNumber`       |                                                      | `certificates[].certificateNumber`                            | `string`                                                                                                                                | Map from the `certificateNumber` field within each object in the `certificates` array.                                                                                       |
| certificates[].certificateType`         |                                                      | `certificates[].certificateType.code`                         | `string`                                                                                                                                | Map the `code` field from the nested `certificateType` object within each object in the `certificates` array.                                                              |
| certificates[].certificateStatus`       |                                                      | `certificates[].certificateStatus.code`                       | `string`                                                                                                                                | Map the `code` field from the nested `certificateStatus` object within each object in the `certificates` array.                                                              |
| certificates[].issueDate`               |                                                      | `certificates[].issueDate`                                    | `2025-04-05`                                                                                                                            | Map from the `issueDate` field within each object in the `certificates` array.                                                                                               |
| certificates[].expiryDate`              |                                                      | `certificates[].expiryDate`                                   | `2025-04-05`                                                                                                                            | Map from the `expiryDate` field within each object in the `certificates` array.                                                                                              |
| certificates[].versionNumber`           |                                                      | `certificates[].versionNumber`                                | `0`                                                                                                                                     | Map from the `versionNumber` field within each object in the `certificates` array.                                                                                     |
| certificates[].versionState`            |                                                      | `certificates[].versionState.code`                            | `string`                                                                                                                                | Map the `code` field from the nested `versionState` object within each object in the `certificates` array.                                                                   |
| certificates[].latestVersion`           |                                                      | `certificates[].latestVersion`                                | `true`                                                                                                                                  | Map from the `latestVersion` field within each object in the `certificates` array.                                                                                     |
| certificates[].lastUpdateDate`          |                                                      | `certificates[].lastUpdateDate`                               | `2025-04-05T13:14:38.173Z`                                                                                                              | Map from the `lastUpdateDate` field within each object in the `certificates` array.                                                                                          |
| certificates[].description`             |                                                      | `certificates[].description.texts[].text`                     | `string`                                                                                                                                | Map the `text` from the first `texts` entry in the nested `description` object within each object in the `certificates` array.                                               |
| certificates[].udiDiDataId`             |                                                      | `certificates[].udiDiDataId`                                  | `0`                                                                                                                                     | Map from the `udiDiDataId` field within each object in the `certificates` array.                                                                                             |
| certificates[].new`                     |                                                      | `certificates[].new`                                          | `true`                                                                                                                                  | Map from the `new` field within each object in the `certificates` array.                                                                                                     |
| regulatoryComplianceResponsibles`       |                                                      | `regulatoryComplianceResponsibles`                            | `[...]` (array of objects)                                                                                                              | Map the array of objects from `getActorDetailsByUuid`. Detailed mapping for objects within this array follows.                                                              |
| regulatoryComplianceResponsibles[].firstName` |                                          | `regulatoryComplianceResponsibles[].firstName`                | `string`                                                                                                                                | Map from the `firstName` field within each object in the `regulatoryComplianceResponsibles` array.                                                                           |
| regulatoryComplianceResponsibles[].familyName` |                                         | `regulatoryComplianceResponsibles[].familyName`               | `string`                                                                                                                                | Map from the `familyName` field within each object in the `regulatoryComplianceResponsibles` array.                                                                          |
| regulatoryComplianceResponsibles[].electronicMail` |                                    | `regulatoryComplianceResponsibles[].electronicMail`           | `<EMAIL>`                                                                                                                      | Map from the `electronicMail` field within each object in the `regulatoryComplianceResponsibles` array.                                                                      |
| regulatoryComplianceResponsibles[].telephone` |                                        | `regulatoryComplianceResponsibles[].telephone`                | `string`                                                                                                                                | Map from the `telephone` field within each object in the `regulatoryComplianceResponsibles` array.                                                                           |
| regulatoryComplianceResponsibles[].position` |                                          | `regulatoryComplianceResponsibles[].position`                 | `string`                                                                                                                                | Map from the `position` field within each object in the `regulatoryComplianceResponsibles` array.                                                                            |
| regulatoryComplianceResponsibles[].geographicalAddress.streetName` |                               | `regulatoryComplianceResponsibles[].geographicalAddress.streetName` | `string`                                                                                                                                | Map from the nested `streetName` field within the `geographicalAddress` object in the `regulatoryComplianceResponsibles` array.                                                |
| regulatoryComplianceResponsibles[].geographicalAddress.streetInfoApplicable` |                         | `regulatoryComplianceResponsibles[].geographicalAddress.streetInfoApplicable` | `true`                                                                                                                                  | Map from the nested `streetInfoApplicable` field within the `geographicalAddress` object in the `regulatoryComplianceResponsibles` array.                                      |
| regulatoryComplianceResponsibles[].geographicalAddress.buildingNumber` |                             | `regulatoryComplianceResponsibles[].geographicalAddress.buildingNumber` | `string`                                                                                                                                | Map from the nested `buildingNumber` field within the `geographicalAddress` object in the `regulatoryComplianceResponsibles` array.                                            |
| regulatoryComplianceResponsibles[].geographicalAddress.complement` |                               | `regulatoryComplianceResponsibles[].geographicalAddress.complement` | `string`                                                                                                                                | Map from the nested `complement` field within the `geographicalAddress` object in the `regulatoryComplianceResponsibles` array.                                                |
| regulatoryComplianceResponsibles[].geographicalAddress.postbox` |                               | `regulatoryComplianceResponsibles[].geographicalAddress.postbox` | `string`                                                                                                                                | Map from the nested `postbox` field within the `geographicalAddress` object in the `regulatoryComplianceResponsibles` array.                                                 |
| regulatoryComplianceResponsibles[].geographicalAddress.gps` |                                   | `regulatoryComplianceResponsibles[].geographicalAddress.gps` | `string`                                                                                                                                | Map from the nested `gps` field within the `geographicalAddress` object in the `regulatoryComplianceResponsibles` array.                                                     |
| regulatoryComplianceResponsibles[].geographicalAddress.cityName` |                               | `regulatoryComplianceResponsibles[].geographicalAddress.cityName` | `string`                                                                                                                                | Map from the nested `cityName` field within the `geographicalAddress` object in the `regulatoryComplianceResponsibles` array.                                                |
| regulatoryComplianceResponsibles[].geographicalAddress.postalZone` |                               | `regulatoryComplianceResponsibles[].geographicalAddress.postalZone` | `string`                                                                                                                                | Map from the nested `postalZone` field within the `geographicalAddress` object in the `regulatoryComplianceResponsibles` array.                                                |
| regulatoryComplianceResponsibles[].geographicalAddress.country.name` |                             | `regulatoryComplianceResponsibles[].geographicalAddress.country.name` | `string`                                                                                                                                | Map from the nested `name` field within the `country` object in the `geographicalAddress` object in the `regulatoryComplianceResponsibles` array.                               |
| regulatoryComplianceResponsibles[].geographicalAddress.country.type` |                             | `regulatoryComplianceResponsibles[].geographicalAddress.country.type` | `string`                                                                                                                                | Map from the nested `type` field within the `country` object in the `geographicalAddress` object in the `regulatoryComplianceResponsibles` array.                               |
| regulatoryComplianceResponsibles[].geographicalAddress.country.iso2Code` |                           | `regulatoryComplianceResponsibles[].geographicalAddress.country.iso2Code` | `string`                                                                                                                                | Map from the nested `iso2Code` field within the `country` object in the `geographicalAddress` object in the `regulatoryComplianceResponsibles` array.                             |
| regulatoryComplianceResponsibles[].geographicalAddress.country.nonEUMemberState` |                     | `regulatoryComplianceResponsibles[].geographicalAddress.country.nonEUMemberState` | `true`                                                                                                                                  | Map from the nested `nonEUMemberState` field within the `country` object in the `geographicalAddress` object in the `regulatoryComplianceResponsibles` array.                   |
| regulatoryComplianceResponsibles[].geographicalAddress.country.type` |                             | `regulatoryComplianceResponsibles[].geographicalAddress.country.type` | `string`                                                                                                                                | Map from the nested `type` field within the `country` object in the `geographicalAddress` object in the `regulatoryComplianceResponsibles` array.                               |
| regulatoryComplianceResponsibles[].geographicalAddress.country.iso2Code` |                           | `regulatoryComplianceResponsibles[].geographicalAddress.country.iso2Code` | `string`                                                                                                                                | Map from the nested `iso2Code` field within the `country` object in the `geographicalAddress` object in the `regulatoryComplianceResponsibles` array.                             |
| regulatoryComplianceResponsibles[].geographicalAddress.country.nonEUMemberState` |                     | `regulatoryComplianceResponsibles[].geographicalAddress.country.nonEUMemberState` | `true`                                                                                                                                  | Map from the nested `nonEUMemberState` field within the `country` object in the `geographicalAddress` object in the `regulatoryComplianceResponsibles` array.                   |
| subcontractors[].subcontractor.actorType` |                                                    | `subcontractors[].subcontractor.actorType.code`               | `string`                                                                                                                                | Map the `code` field from the nested `actorType` object within the `subcontractor` object in the `subcontractors` array.                                                     |
| subcontractors[].subcontractor.name`    |                                                      | `subcontractors[].subcontractor.name`                         | `string`                                                                                                                                | Map from the nested `name` field within the `subcontractor` object in the `subcontractors` array.                                                                            |
| subcontractors[].subcontractor.countryIso2Code` |                                          | `subcontractors[].subcontractor.countryIso2Code`              | `string`                                                                                                                                | Map from the nested `countryIso2Code` field within the `subcontractor` object in the `subcontractors` array. This seems to be a direct field, not nested under address. **Doubt** |
| subcontractors[].subcontractor.actorStatus.startDate` |                                    | `subcontractors[].subcontractor.statusFromDate`               | `2025-04-05T13:14:38.173Z`                                                                                                              | Map from the nested `statusFromDate` field within the `subcontractor` object's status in the `subcontractors` array.                                                             |
| subcontractors[].subcontractor.actorStatus.endDate` |                                      |                                                               | `2025-04-05T13:14:38.173Z`                                                                                                              | Source for this field within the nested subcontractor's actor status is not clear from the schema. **Doubt**                                                                   |
| subcontractors[].subcontractor.actorStatus.versionNumber` |                                | `subcontractors[].subcontractor.versionNumber`                | `0`                                                                                                                                     | Map from the nested `versionNumber` field within the `subcontractor` object's status in the `subcontractors` array.                                                              |
| subcontractors[].subcontractor.actorStatus.versionState` |                                | `subcontractors[].subcontractor.versionState.code`             | `string`                                                                                                                                | Map the `code` field from the nested `versionState` object within the `subcontractor` object's status in the `subcontractors` array.                                            |
| subcontractors[].subcontractor.actorStatus.statusFromDate` |                               | `subcontractors[].subcontractor.statusFromDate`               | `2025-04-05`                                                                                                                            | Map from the nested `statusFromDate` field within the `subcontractor` object's status in the `subcontractors` array.                                                             |
| subcontractors[].subcontractor.actorStatus.latestVersion` |                               | `subcontractors[].subcontractor.latestVersion`                | `true`                                                                                                                                  | Map from the nested `latestVersion` field within the `subcontractor` object's status in the `subcontractors` array.                                                              |
| subcontractors[].subcontractor.actorStatus.lastUpdateDate` |                               | `subcontractors[].subcontractor.lastUpdateDate`               | `2025-04-05T13:14:38.173Z`                                                                                                              | Map from the nested `lastUpdateDate` field within the `subcontractor` object's status in the `subcontractors` array.                                                             |
| subcontractors[].subcontractor.actorStatus.status` |                                      | `subcontractors[].subcontractor.status.code`                  | `string`                                                                                                                                | Map the `code` field from the nested `status` object within the `subcontractor` object's status in the `subcontractors` array.                                                 |
| subcontractors[].subcontractor.names`   |                                                      | `subcontractors[].subcontractor.names`                        | `{...}` (object)                                                                                                                        | Map the nested `names` object from within the `subcontractor` object in the `subcontractors` array. This object needs further mapping (abbreviated, language, name). |
| subcontractors[].subcontractor.names.abbreviated` |                                        |                                                               | `true`                                                                                                                                  | Indicates whether the record comes from abbreviatedNames (true) or from names (false) within the nested names object. **Doubt**                                             |
| subcontractors[].subcontractor.names.language` |                                        | `subcontractors[].subcontractor.names.texts[].language.isoCode` | `string`                                                                                                                                | Map the `isoCode` from the `language` object within the first `texts` entry in the nested `names` object.                                                                    |
| subcontractors[].subcontractor.names.name` |                                              | `subcontractors[].subcontractor.names.texts[].text`           | `string`                                                                                                                                | Map the `text` from the first `texts` entry in the nested `names` object.                                                                                                    |
| subcontractors[].subcontractor.address.countryIso2Code` |                                  | `subcontractors[].subcontractor.countryIso2Code`              | `string`                                                                                                                                | Map from the nested `countryIso2Code` field within the `subcontractor` object in the `subcontractors` array. This seems to be a direct field, not nested under address. **Doubt** |
| subcontractors[].subcontractor.address.countryName` |                                    | `subcontractors[].subcontractor.countryName`                  | `string`                                                                                                                                | Map from the nested `countryName` field within the `subcontractor` object in the `subcontractors` array. This seems to be a direct field, not nested under address. **Doubt** |
| subcontractors[].subcontractor.address.geographicalAddress` |                               | `subcontractors[].subcontractor.geographicalAddress`           | `string`                                                                                                                                | Map from the nested `geographicalAddress` field within the `subcontractor` object in the `subcontractors` array. This seems to be a direct field, not nested under address. **Doubt** |
| subcontractors[].subcontractor.contact.firstName` |                                      |                                                               | `string`                                                                                                                                | Source for contact details within the nested subcontractor is not clear from the schema. **Doubt**                                                                       |
| subcontractors[].subcontractor.contact.familyName` |                                     |                                                               | `string`
| subcontractorHistory[].subcontractor.contact.website` |                                   |                                                               | `string`                                                                                                                                | Source for website within the nested subcontractor is not clear from the schema. **Doubt**                                                                       |
| certificates`                           |                                                      | `certificates`                                                | `[...]` (array of objects)                                                                                                              | Map the array of objects from `getActorDetailsByUuid`. Detailed mapping for objects within this array follows.                                                              |
| certificates[].uuid`                    |                                                      | `certificates[].uuid`                                         | `string`                                                                                                                                | Map from the `uuid` field within each object in the `certificates` array.                                                                                                  |
| certificates[].certificateNumber`       |                                                      | `certificates[].certificateNumber`                            | `string`                                                                                                                                | Map from the `certificateNumber` field within each object in the `certificates` array.                                                                                       |
| certificates[].certificateType`         |                                                      | `certificates[].certificateType.code`                         | `string`                                                                                                                                | Map the `code` field from the nested `certificateType` object within each object in the `certificates` array.                                                              |
| certificates[].certificateStatus`       |                                                      | `certificates[].certificateStatus.code`                       | `string`                                                                                                                                | Map the `code` field from the nested `certificateStatus` object within each object in the `certificates` array.                                                              |
| certificates[].issueDate`               |                                                      | `certificates[].issueDate`                                    | `2025-04-05`                                                                                                                            | Map from the `issueDate` field within each object in the `certificates` array.                                                                                               |
| certificates[].expiryDate`              |                                                      | `certificates[].expiryDate`                                   | `2025-04-05`                                                                                                                            | Map from the `expiryDate` field within each object in the `certificates` array.                                                                                              |
| certificates[].versionNumber`           |                                                      | `certificates[].versionNumber`                                | `0`                                                                                                                                     | Map from the `versionNumber` field within each object in the `certificates` array.                                                                                     |
| certificates[].versionState`            |                                                      | `certificates[].versionState.code`                            | `string`                                                                                                                                | Map the `code` field from the nested `versionState` object within each object in the `certificates` array.                                                                   |
| certificates[].latestVersion`           |                                                      | `certificates[].latestVersion`                                | `true`                                                                                                                                  | Map from the `latestVersion` field within each object in the `certificates` array.                                                                                     |
| certificates[].lastUpdateDate`          |                                                      | `certificates[].lastUpdateDate`                               | `2025-04-05T13:14:38.173Z`                                                                                                              | Map from the `lastUpdateDate` field within each object in the `certificates` array.                                                                                          |
| certificates[].description`             |                                                      | `certificates[].description.texts[].text`                     | `string`                                                                                                                                | Map the `text` from the first `texts` entry in the nested `description` object within each object in the `certificates` array.                                               |
| certificates[].udiDiDataId`             |                                                      | `certificates[].udiDiDataId`                                  | `0`                                                                                                                                     | Map from the `udiDiDataId` field within each object in the `certificates` array.                                                                                             |
| certificates[].new`                     |                                                      | `certificates[].new`                                          | `true`                                                                                                                                  | Map from the `new` field within each object in the `certificates` array.                                                                                                     |
| regulatoryComplianceResponsibles`       |                                                      | `regulatoryComplianceResponsibles`                            | `[...]` (array of objects)                                                                                                              | Map the array of objects from `getActorDetailsByUuid`. Detailed mapping for objects within this array follows.                                                              |
| regulatoryComplianceResponsibles[].firstName` |                                          | `regulatoryComplianceResponsibles[].firstName`                | `string`                                                                                                                                | Map from the `firstName` field within each object in the `regulatoryComplianceResponsibles` array.                                                                           |
| regulatoryComplianceResponsibles[].familyName` |                                         | `regulatoryComplianceResponsibles[].familyName`               | `string`                                                                                                                                | Map from the `familyName` field within each object in the `regulatoryComplianceResponsibles` array.                                                                          |
| regulatoryComplianceResponsibles[].electronicMail` |                                    | `regulatoryComplianceResponsibles[].electronicMail`           | `<EMAIL>`                                                                                                                      | Map from the `electronicMail` field within each object in the `regulatoryComplianceResponsibles` array.                                                                      |
| regulatoryComplianceResponsibles[].telephone` |                                        | `regulatoryComplianceResponsibles[].telephone`                | `string`                                                                                                                                | Map from the `telephone` field within each object in the `regulatoryComplianceResponsibles` array.                                                                           |
| regulatoryComplianceResponsibles[].position` |                                          | `regulatoryComplianceResponsibles[].position`                 | `string`                                                                                                                                | Map from the `position` field within each object in the `regulatoryComplianceResponsibles` array.                                                                            |
| regulatoryComplianceResponsibles[].geographicalAddress.streetName` |                               | `regulatoryComplianceResponsibles[].geographicalAddress.streetName` | `string`                                                                                                                                | Map from the nested `streetName` field within the `geographicalAddress` object in the `regulatoryComplianceResponsibles` array.                                                |
| regulatoryComplianceResponsibles[].geographicalAddress.streetInfoApplicable` |                         | `regulatoryComplianceResponsibles[].geographicalAddress.streetInfoApplicable` | `true`
| regulatoryComplianceResponsibles[].geographicalAddress.buildingNumber` |                             | `regulatoryComplianceResponsibles[].geographicalAddress.buildingNumber` | `string`                                                                                                                                | Map from the nested `buildingNumber` field within the `geographicalAddress` object in the `regulatoryComplianceResponsibles` array.                                            |
| regulatoryComplianceResponsibles[].geographicalAddress.complement` |                               | `regulatoryComplianceResponsibles[].geographicalAddress.complement` | `string`                                                                                                                                | Map from the nested `complement` field within the `geographicalAddress` object in the `regulatoryComplianceResponsibles` array.                                                |
| regulatoryComplianceResponsibles[].geographicalAddress.postbox` |                               | `regulatoryComplianceResponsibles[].geographicalAddress.postbox` | `string`                                                                                                                                | Map from the nested `postbox` field within the `geographicalAddress` object in the `regulatoryComplianceResponsibles` array.                                                 |
| regulatoryComplianceResponsibles[].geographicalAddress.gps` |                                   | `regulatoryComplianceResponsibles[].geographicalAddress.gps` | `string`                                                                                                                                | Map from the nested `gps` field within the `geographicalAddress` object in the `regulatoryComplianceResponsibles` array.                                                     |
| regulatoryComplianceResponsibles[].geographicalAddress.cityName` |                               | `regulatoryComplianceResponsibles[].geographicalAddress.cityName` | `string`                                                                                                                                | Map from the nested `cityName` field within the `geographicalAddress` object in the `regulatoryComplianceResponsibles` array.                                                |
| regulatoryComplianceResponsibles[].geographicalAddress.postalZone` |                               | `regulatoryComplianceResponsibles[].geographicalAddress.postalZone` | `string`                                                                                                                                | Map from the nested `postalZone` field within the `geographicalAddress` object in the `regulatoryComplianceResponsibles` array.                                                |
| regulatoryComplianceResponsibles[].geographicalAddress.country.name` |                             | `regulatoryComplianceResponsibles[].geographicalAddress.country.name` | `string`                                                                                                                                | Map from the nested `name` field within the `country` object in the `geographicalAddress` object in the `regulatoryComplianceResponsibles` array.                               |
| regulatoryComplianceResponsibles[].geographicalAddress.country.type` |                             | `regulatoryComplianceResponsibles[].geographicalAddress.country.type` | `string`                                                                                                                                | Map from the nested `type` field within the `country` object in the `geographicalAddress` object in the `regulatoryComplianceResponsibles` array.                               |
| regulatoryComplianceResponsibles[].geographicalAddress.country.iso2Code` |                           | `regulatoryComplianceResponsibles[].geographicalAddress.country.iso2Code` | `string`                                                                                                                                | Map from the nested `iso2Code` field within the `country` object in the `geographicalAddress` object in the `regulatoryComplianceResponsibles` array.                             |
| regulatoryComplianceResponsibles[].geographicalAddress.country.nonEUMemberState` |                     | `regulatoryComplianceResponsibles[].geographicalAddress.country.nonEUMemberState` | `true`                                                                                                                                  | Map from the nested `nonEUMemberState` field within the `country` object in the `geographicalAddress` object in the `regulatoryComplianceResponsibles` array.                   |
