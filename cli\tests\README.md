# CLI Tests

This directory contains the test suite for the EUDAMED CLI data processing tool.

## Directory Structure

```
tests/
├── README.md                    # This file
├── run-tests.js                 # Test runner script
├── unit/                        # Unit tests
│   └── version-conflict-resolution.test.js
├── integration/                 # Integration tests
│   └── (future test files)
└── fixtures/                    # Test data and fixtures
    └── (test data files)
```

## Test Categories

### Unit Tests (`unit/`)
Tests for individual functions and modules in isolation:
- **version-conflict-resolution.test.js**: Tests the version comparison logic used in database upsert operations

### Integration Tests (`integration/`)
Tests for complete workflows and interactions between components:
- Database operations with real SQLite instances
- API endpoint testing
- Data processing pipeline tests

### Fixtures (`fixtures/`)
Test data and mock responses:
- Sample EUDAMED API responses
- Test database schemas
- Mock device data

## Running Tests

### Run All Tests
```bash
# From the cli directory
node tests/run-tests.js

# Or run individual test files
node tests/unit/version-conflict-resolution.test.js
```

### Run Specific Test Categories
```bash
# Unit tests only
node tests/run-tests.js --unit

# Integration tests only
node tests/run-tests.js --integration
```

## Test Development Guidelines

### 1. Test File Naming
- Unit tests: `*.test.js`
- Integration tests: `*.integration.test.js`
- Use descriptive names that match the functionality being tested

### 2. Test Structure
Each test file should include:
- Clear documentation of what is being tested
- Test coverage information
- Proper error handling and edge cases
- Export functions for use in other tests

### 3. Adding New Tests
When implementing new functionality:

1. **Create test file first** (TDD approach recommended)
2. **Write tests for edge cases** including error conditions
3. **Update this README** if adding new test categories
4. **Run existing tests** to ensure no regressions

### 4. Test Data
- Place test data in `fixtures/` directory
- Use realistic but anonymized data
- Document the source and purpose of test data

## Test Coverage Requirements

### Database Operations
- [ ] Version conflict resolution ✅
- [ ] Database initialization and schema creation
- [ ] Batch insert operations
- [ ] Error handling and recovery

### Data Processing
- [ ] Device data mapping and transformation
- [ ] API response parsing
- [ ] Pagination handling
- [ ] Concurrency control

### CLI Interface
- [ ] Command-line argument parsing
- [ ] Progress bar functionality
- [ ] Error reporting and logging
- [ ] File I/O operations

## Continuous Integration

Tests should be run:
- Before committing changes
- As part of the development workflow
- When modifying database schemas or data processing logic

## Future Enhancements

- [ ] Add test coverage reporting
- [ ] Implement automated test discovery
- [ ] Add performance benchmarking tests
- [ ] Create mock EUDAMED API server for integration tests
- [ ] Add database migration tests
