openapi: 3.0.3
info:
  title: EUDAMED Public Device Search API (Reverse-Engineered)
  description: |
    An *unofficial* OpenAPI specification based on observed network traffic
    for the EUDAMED public device search API endpoint (`/api/devices/udiDiData`).

    This contract is derived from sample URLs and partial documentation.
    **The response schemas need to be defined based on actual API responses.**
  version: 1.0.0
servers:
  - url: https://ec.europa.eu/tools/eudamed # Base URL observed from sample
    description: EUDAMED API Server

tags:
  - name: Medical Devices
    description: Operations related to searching and retrieving medical device data.
  - name: Actors
    description: Operations related to searching and retrieving actor data.

paths:
  /api/eos:
    get:
      tags:
        - Actors
      summary: Search for Actors
      description: |
        Retrieves a paginated list of actors matching the specified search criteria.
        Filters can be combined.
      operationId: searchActors
      parameters:
        # --- Pagination ---
        - name: page
          in: query
          description: Page number for pagination (starts from 0).
          required: false
          schema: { type: integer, format: int32, minimum: 0, default: 0 }
          example: 0
        - name: size
          in: query
          description: Number of results per page.
          required: false
          schema: { type: integer, format: int32, minimum: 1, maximum: 50, default: 25 }
          example: 25
        - name: pageSize
          in: query
          description: Alias for size parameter.
          required: false
          schema: { type: integer, format: int32, minimum: 1, maximum: 50, default: 25 }
          example: 25
        - name: rnd
          in: query
          description: Random number to prevent caching (timestamp).
          required: false
          schema: { type: integer, format: int64 }
          example: 1743752364426
        - name: sort
          in: query
          description: Sorting criteria in the format 'property,direction'. Multiple sort parameters are supported.
          required: false
          schema:
            type: array
            items: { type: string }
          example: ["srn,ASC", "versionNumber,DESC"]
          style: form
          explode: true

        # --- Actor Filters ---
        - name: name
          in: query
          description: Filter by Actor Name.
          required: false
          schema: { type: string }
          example: "param-name"
        - name: srn
          in: query
          description: Filter by Actor's Single Registration Number (SRN).
          required: false
          schema: { type: string, pattern: '^([A-Z]{2})-([A-Z]{2})-([0-9]{9})$' }
          example: "param-srn"
        - name: actorTypeCode
          in: query
          description: Filter by Actor Type Code.
          required: false
          schema:
            type: string
            enum:
              - refdata.actor-type.authorised-representative
              - refdata.actor-type.importer
              - refdata.actor-type.manufacturer
              - refdata.actor-type.system-procedure-pack-producer
          example: "refdata.actor-type.manufacturer"
        - name: arImporterName
          in: query
          description: Filter by Authorised Representative or Importer Name (only applicable when actorTypeCode is manufacturer).
          required: false
          schema: { type: string }
          example: "param-ar-importer-name"
        - name: arImporterSrn
          in: query
          description: Filter by Authorised Representative or Importer SRN (only applicable when actorTypeCode is manufacturer).
          required: false
          schema: { type: string, pattern: '^([A-Z]{2})-([A-Z]{2})-([0-9]{9})$' }
          example: "param-ar-importer-srn"
        - name: countryIso2Code
          in: query
          description: Filter by Country ISO 2 Code.
          required: false
          schema: { type: string, pattern: '^[A-Z]{2}$' }
          example: "BE"
        - name: validatorUuid
          in: query
          description: Validator UUID (purpose unclear).
          required: false
          schema: { type: string, format: uuid }
          example: "02210dfd-8057-4c59-88bf-c753e82d5780"
        - name: languageIso2Code
          in: query
          description: ISO 639-1 code for the language of the results.
          required: false
          schema: { type: string, pattern: '^[a-z]{2}$', default: "en" }
          example: "en"
      responses:
        '200':
          description: Successful retrieval of actors.
          content:
            application/json:
              schema: { $ref: '#/components/schemas/ActorSearchResponse' }
        '400':
          description: Bad Request - Invalid parameter format or value.
          content:
            application/json:
              schema: { $ref: '#/components/schemas/ErrorResponse' }
        '500':
          description: Internal Server Error.
          content:
            application/json:
              schema: { $ref: '#/components/schemas/ErrorResponse' }

  /api/actors/{uuid}/publicInformation:
    get:
      tags:
        - Actors
      summary: Get Specific Actor Details
      description: |
        Retrieves detailed information for a specific actor identified by its UUID.
      operationId: getActorDetailsByUuid
      parameters:
        - name: uuid
          in: path
          description: The UUID of the actor record (obtained from search results).
          required: true
          schema: { type: string, format: uuid }
          example: "ca120f4f-ef0b-4a6d-a0b0-9ccec44ccb57"
        - name: languageIso2Code
          in: query
          description: ISO 639-1 code for the language of the results.
          required: false
          schema: { type: string, pattern: '^[a-z]{2}$', default: "en" }
          example: "en"
      responses:
        '200':
          description: Successful retrieval of actor details.
          content:
            application/json:
              schema: { $ref: '#/components/schemas/ActorDetailResponse' }
        '400':
          description: Bad Request - Invalid UUID format or invalid language code.
          content:
            application/json:
              schema: { $ref: '#/components/schemas/ErrorResponse' }
        '404':
          description: Not Found - No actor found for the provided UUID.
          content:
            application/json:
              schema: { $ref: '#/components/schemas/ErrorResponse' }
        '500':
          description: Internal Server Error.
          content:
            application/json:
              schema: { $ref: '#/components/schemas/ErrorResponse' }

components:
  schemas:

    PaginationInfo:
       description: Structure containing pagination details (extracted from top-level response).
       type: object
       properties:
         first: { type: boolean, description: "Indicates if it is the first page." }
         last: { type: boolean, description: "Indicates if it is the last page." }
         number: { type: integer, format: int32, description: "Current page number (0-based)." }
         numberOfElements: { type: integer, format: int32, description: "Number of elements on the current page." }
         size: { type: integer, format: int32, description: "Page size requested." }
         sort: { type: array, description: "Sorting criteria applied to the results.", items: { $ref: '#/components/schemas/SortInfo' } }
         totalElements: { type: integer, format: int64, description: "Total number of items matching the query across all pages." }
         totalPages: { type: integer, format: int32, description: "Total number of pages available." }
    SortInfo:
      description: Represents one sorting criterion.
      type: object
      properties:
        ascending: { type: boolean, nullable: true }
        descending: { type: boolean, nullable: true }
        direction: { type: string, enum: [ASC, DESC], nullable: true }
        ignoreCase: { type: boolean, nullable: true }
        nullHandling: { type: string, enum: [NATIVE, NULLS_FIRST, NULLS_LAST], nullable: true }
        property: { type: string, nullable: true }
    ErrorResponse:
      description: Generic error structure (Placeholder - Verify actual error format).
      type: object
      properties:
        timestamp: { type: string, format: date-time, description: "Time the error occurred." }
        status: { type: integer, format: int32, description: "HTTP Status code repeated.", example: 400 }
        error: { type: string, description: "Short error description (e.g., 'Bad Request').", example: "Bad Request" }
        message: { type: string, description: "Detailed error message.", example: "Invalid risk class code provided." }
        path: { type: string, description: "The API path that caused the error.", example: "/api/devices/udiDiData" }
    Sort:
      description: Sorting information.
      type: object
      properties:
        sorted: { type: boolean }
        unsorted: { type: boolean }
        empty: { type: boolean }
    Pageable:
      description: Pagination information.
      type: object
      properties:
        sort: { $ref: '#/components/schemas/Sort' }
        offset: { type: integer }
        pageNumber: { type: integer }
        pageSize: { type: integer }
        paged: { type: boolean }
        unpaged: { type: boolean }


    #actor
    ActorSearchResponse:
      description: Response from the actor search endpoint.
      type: object
      properties:
        content: { type: array, items: { $ref: '#/components/schemas/ActorSummary' } }
        pageable: { $ref: '#/components/schemas/Pageable' }
        totalPages: { type: integer }
        totalElements: { type: integer }
        last: { type: boolean }
        size: { type: integer }
        number: { type: integer }
        sort: { $ref: '#/components/schemas/Sort' }
        numberOfElements: { type: integer }
        first: { type: boolean }
        empty: { type: boolean }

    ActorSummary:
      description: Basic summary of an Actor (Manufacturer, Notified Body, Authorised Representative).
      type: object
      nullable: true
      properties:
        uuid: { type: string, format: uuid, nullable: true }
        srn: { type: string, nullable: true }
        actorType: { type: string, nullable: true } # e.g., refdata.actor-type.manufacturer
        countryIso2Code: { type: string, nullable: true }
        name: { type: string, nullable: true }
        names: { $ref: '#/components/schemas/TranslatedText', nullable: true }
        abbreviatedNames: { $ref: '#/components/schemas/TranslatedText', nullable: true }
        countryName: { type: string, nullable: true }
        countryType: { type: string, nullable: true }
        geographicalAddress: { type: string, nullable: true }
        electronicMail: { type: string, format: email, nullable: true }
        telephone: { type: string, nullable: true }
        status: { type: string, nullable: true }
        statusFromDate: { type: string, format: date, nullable: true }
        versionNumber: { type: integer, nullable: true }
        versionState: { type: string, nullable: true } #e.g., 'refdata.eudamed-entity-version-status.registered'
        latestVersion: { type: boolean, nullable: true }
        lastUpdateDate: { type: string, format: date-time, nullable: true }

    AuthorisedRepresentativeSummary:
      description: Specific summary for an Authorised Representative linked to a Manufacturer.
      type: object
      nullable: true
      properties:
        nonEuManufacturerUuid: { type: string, format: uuid, nullable: true }
        authorisedRepresentativeUuid: { type: string, format: uuid, nullable: true }
        srn: { type: string, nullable: true }
        name: { type: string, nullable: true }
        countryName: { type: string, nullable: true }
        address: { type: string, nullable: true }
        email: { type: string, format: email, nullable: true }
        telephone: { type: string, nullable: true }
        startDate: { type: string, format: date, nullable: true }
        endDate: { type: string, format: date, nullable: true }
        mandateStatus: { type: object, nullable: true }
        terminationDate: { type: string, format: date, nullable: true }
        actorStatus: { type: string, nullable: true } # Assuming RefDataCode
        actorStatusFromDate: { type: string, format: date, nullable: true }
        versionNumber: { type: integer, nullable: true }
        versionState: { type: string, nullable: true } #e.g., 'refdata.eudamed-entity-version-status.registered'
        latestVersion: { type: boolean, nullable: true }
        lastUpdateDate: { type: string, format: date-time, nullable: true }

    ActorAddress:
      description: Detailed address information for an actor.
      type: object
      nullable: true
      properties:
        streetName: { type: string, nullable: true }
        streetInfoApplicable: { type: boolean, nullable: true }
        buildingNumber: { type: string, nullable: true }
        complement: { type: string, nullable: true }
        postbox: { type: string, nullable: true }
        gps: { type: string, nullable: true }
        cityName: { type: string, nullable: true }
        postalZone: { type: string, nullable: true }
        country: { $ref: '#/components/schemas/Country', nullable: true }

    ActorDetailResponse: #merge of public actor data and actor detail response
      description: Detailed information about an actor.
      type: object
      properties:
        #identification
        srn: { type: string, nullable: true } #eudamedIdentifier
        europeanVatNumberApplicable: { type: boolean, nullable: true }
        europeanVatNumber: { type: string, nullable: true }
        tradeRegister: { type: string, nullable: true }
        eori: { type: string, nullable: true }
        type: { $ref: '#/components/schemas/ActorType', nullable: true}
        country: { $ref: '#/components/schemas/Country', nullable: true }
        competentAuthorityResponsibility: { type: object, nullable: true }
        organizationIdentificationDocuments: { type: array, items: { type: object }, nullable: true }
        #properties
        importers: { type: array, items: { $ref: '#/components/schemas/ImporterInfo' }, nullable: true, description: "List of importers associated with this actor (only applicable for manufacturers)" }
        nonEuManufacturers: { type: array, items: { $ref: '#/components/schemas/ActorSummary' }, nullable: true, description: "List of non-EU manufacturers associated with this actor (only applicable for importers and authorized representatives)" }
        mandates: { type: array, items: { $ref: '#/components/schemas/Mandate' }, nullable: true }
        mandateHistory: { type: array, items: { $ref: '#/components/schemas/MandateHistory' }, nullable: true }
        subcontractors: { type: array, items: { $ref: '#/components/schemas/Subcontractor' }, nullable: true }
        subcontractorHistory: { type: array, items: { $ref: '#/components/schemas/SubcontractorHistory' }, nullable: true }
        certificates: { type: array, items: { $ref: '#/components/schemas/Certificate' }, nullable: true }
        authorisedRepresentatives:
          type: array
          items: { $ref: '#/components/schemas/AuthorisedRepresentativeSummary' }
          nullable: true
        #contacts
        telephone: { type: string, nullable: true }
        electronicMail: { type: string, format: email, nullable: true }
        website: { type: string, format: uri, nullable: true }
        actorAddress:
          $ref: '#/components/schemas/ActorAddress'
          nullable: true
        validatorName: { type: string, nullable: true }
        validatorType:
          $ref: '#/components/schemas/ActorType'
          nullable: true
        validatorSrn: { type: string, nullable: true } # Reverted simple type for safety
        validatorAddress:
          $ref: '#/components/schemas/ActorAddress'
          nullable: true
        validatorEmail: { type: string, format: email, nullable: true }
        validatorTelephone: { type: string, nullable: true }
        regulatoryComplianceResponsibles:
          type: array
          items: { $ref: '#/components/schemas/RegulatoryComplianceResponsible' }
          nullable: true
        legislationLinks:
          type: array
          items: { type: string }
          nullable: true
        certificates:
          type: array
          items: { $ref: '#/components/schemas/Certificate' }
          nullable: true
        #status
        latestVersion: { type: boolean, nullable: true }
        versionNumber: { type: integer, nullable: true }
        versionState:
          $ref: '#/components/schemas/VersionState'
          nullable: true
        lastUpdateDate: { type: string, format: date-time, nullable: true }
        actorStatus: { type: string, nullable: true }
        actorStatusFromDate: { type: string, format: date-time, nullable: true }

    Mandate:
      description: Mandate information.
      type: object
      properties:
        uuid: { type: string, format: uuid }
        mandator: { $ref: '#/components/schemas/ActorSummary' }
        mandatee: { $ref: '#/components/schemas/ActorSummary' }
        startDate: { type: string, format: date }
        endDate: { type: string, format: date, nullable: true }
        status: { type: string }
        versionNumber: { type: integer }
        versionState: { type: string, nullable: true } #e.g., 'refdata.eudamed-entity-version-status.registered'
        latestVersion: { type: boolean }
        lastUpdateDate: { type: string, format: date-time }

    MandateHistory:
      description: Mandate history information.
      type: object
      properties:
        uuid: { type: string, format: uuid }
        mandator: { $ref: '#/components/schemas/ActorSummary' }
        mandatee: { $ref: '#/components/schemas/ActorSummary' }
        startDate: { type: string, format: date }
        endDate: { type: string, format: date, nullable: true }
        status: { type: string }
        versionNumber: { type: integer }
        versionState: { type: string, nullable: true } #e.g., 'refdata.eudamed-entity-version-status.registered'
        latestVersion: { type: boolean }
        lastUpdateDate: { type: string, format: date-time }

    Subcontractor:
      description: Subcontractor information.
      type: object
      properties:
        uuid: { type: string, format: uuid }
        contractor: { $ref: '#/components/schemas/ActorSummary' }
        subcontractor: { $ref: '#/components/schemas/ActorSummary' }
        startDate: { type: string, format: date }
        endDate: { type: string, format: date, nullable: true }
        status: { type: string }
        versionNumber: { type: integer }
        versionState: { type: string, nullable: true } #e.g., 'refdata.eudamed-entity-version-status.registered'
        latestVersion: { type: boolean }
        lastUpdateDate: { type: string, format: date-time }

    SubcontractorHistory:
      description: Subcontractor history information.
      type: object
      properties:
        uuid: { type: string, format: uuid }
        contractor: { $ref: '#/components/schemas/ActorSummary' }
        subcontractor: { $ref: '#/components/schemas/ActorSummary' }
        startDate: { type: string, format: date }
        endDate: { type: string, format: date, nullable: true }
        status: { type: string }
        versionNumber: { type: integer }
        versionState: { type: string, nullable: true } #e.g., 'refdata.eudamed-entity-version-status.registered'
        latestVersion: { type: boolean }
        lastUpdateDate: { type: string, format: date-time }

    Certificate:
      description: Certificate information.
      type: object
      properties:
        uuid: { type: string, format: uuid }
        certificateNumber: { type: string }
        certificateType: { type: string }
        certificateStatus: { type: string }
        description: { $ref: '#/components/schemas/TranslatedText', nullable: true }
        udiDiDataId: { type: integer, nullable: true, description: "Link back to the parent UDI DI data record ID?" }
        issueDate: { type: string, format: date }
        expiryDate: { type: string, format: date, nullable: true }
        versionNumber: { type: integer }
        versionState: { type: string, nullable: true } #e.g., 'refdata.eudamed-entity-version-status.registered'
        latestVersion: { type: boolean }
        lastUpdateDate: { type: string, format: date-time }


    RegulatoryComplianceResponsible:
      description: Information about a person responsible for regulatory compliance.
      type: object
      nullable: true
      properties:
        firstName: { type: string, nullable: true }
        familyName: { type: string, nullable: true }
        electronicMail: { type: string, format: email, nullable: true }
        telephone: { type: string, nullable: true }
        position: { type: string, nullable: true }
        geographicalAddress: { $ref: '#/components/schemas/ActorAddress', nullable: true }

    ImporterInfo:
      description: Information about an importer associated with a manufacturer.
      type: object
      nullable: true
      properties:
        uuid: { type: string, format: uuid, nullable: true, description: "UUID of the importer relationship" }
        actor: { $ref: '#/components/schemas/ActorSummary', nullable: true, description: "Detailed information about the importer actor" }
        relatedActorSrn: { type: string, nullable: true, description: "SRN of the related actor" }
        relatedActorName: { type: string, nullable: true, description: "Name of the related actor" }
        relatedActorCountryIsoCode: { type: string, nullable: true, description: "Country ISO code of the related actor" }
        relatedActorAddress: { type: string, nullable: true, description: "Address of the related actor" }
        relatedActorEmail: { type: string, format: email, nullable: true, description: "Email of the related actor" }
        relatedActorPhone: { type: string, nullable: true, description: "Phone number of the related actor" }
        startDate: { type: string, format: date-time, nullable: true, description: "Start date of the importer relationship" }
        endDate: { type: string, format: date-time, nullable: true, description: "End date of the importer relationship, null if still active" }

    TranslatedText:
      description: Represents text that can have multiple language translations.
      type: object
      nullable: true
      properties:
        texts: { type: array, nullable: true, items: { $ref: '#/components/schemas/TextEntry' } }

    TextEntry:
      description: A single text entry, potentially with language information.
      type: object
      nullable: true
      properties:
        language: { type: string, nullable: true, description: "language ISO Code" }
        text: { type: string, nullable: true }

    Country:
      description: Represents a country with its details.
      type: object
      nullable: true
      properties:
        name: { type: string, nullable: true }
        type: { type: string, nullable: true, description: "e.g., EU_MEMBER_STATE, NON_EU, EU_EXTENDED, EU_SPECIAL" }
        iso2Code: { type: string, nullable: true }