#!/usr/bin/env node

/**
 * Integration tests for field completeness conflict resolution
 * 
 * This test suite verifies that the enhanced version conflict resolution
 * works correctly with real database operations, specifically testing
 * field completeness comparison for equal versions.
 * 
 * Test Coverage:
 * - Field completeness comparison with device_details table
 * - Version conflict resolution with complex nested objects
 * - JSON field handling in completeness calculation
 * - Real database scenarios with versionNumber field
 * 
 * <AUTHOR> Data Processing Team
 * @since 2024-01-XX
 */

import { DeviceDatabase } from '../../src/lib/database.js';
import { existsSync, unlinkSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Test database path
const TEST_DB_PATH = join(__dirname, '../fixtures/test-field-completeness.db');

/**
 * Test runner for field completeness integration tests
 */
async function runFieldCompletenessTests() {
  console.log("🧪 Running Integration Tests: Field Completeness Conflict Resolution");
  console.log("=".repeat(75));
  console.log();

  let passed = 0;
  let failed = 0;
  const failures = [];

  // Clean up any existing test database
  if (existsSync(TEST_DB_PATH)) {
    unlinkSync(TEST_DB_PATH);
  }

  let db;

  try {
    // Test 1: Database initialization with versionNumber field
    console.log("✅ Test 01: Database initialization with versionNumber in device_details");
    db = new DeviceDatabase(TEST_DB_PATH);
    await db.initialize();
    passed++;

    // Test 2: Insert initial device details record
    console.log("✅ Test 02: Insert initial device details record");
    const initialDevice = {
      uuid: "field-test-001",
      basicUdi: "12345678901234",
      primaryDi: "12345678901234567890",
      deviceName: "Test Device",
      versionNumber: "1"
    };
    
    const insertCount = db.saveDeviceDetails([initialDevice]);
    if (insertCount === 1) {
      passed++;
    } else {
      failed++;
      failures.push("Initial device details insertion failed");
    }

    // Test 3: Update with same version but more complete record
    console.log("✅ Test 03: Update with same version but more complete record");
    const moreCompleteDevice = {
      uuid: "field-test-001",
      basicUdi: "12345678901234",
      primaryDi: "12345678901234567890",
      deviceName: "Test Device",
      legislation: "MDR",
      riskClass: "IIa",
      manufacturer: {
        name: "Acme Medical Corp",
        srn: "DE-MF-000012345"
      },
      authorisedRepresentative: {
        name: "EU Rep Corp",
        srn: "DE-AR-000067890"
      },
      versionNumber: "1"
    };

    const updateCount = db.saveDeviceDetails([moreCompleteDevice]);
    if (updateCount === 1) {
      passed++;
    } else {
      failed++;
      failures.push("More complete device details update failed");
    }

    // Test 4: Try to update with same version but less complete record
    console.log("✅ Test 04: Attempt update with same version but less complete record");
    const lessCompleteDevice = {
      uuid: "field-test-001",
      basicUdi: "12345678901234",
      primaryDi: "12345678901234567890",
      deviceName: "Test Device Updated", // Different name but fewer fields
      versionNumber: "1"
    };

    const skipCount = db.saveDeviceDetails([lessCompleteDevice]);
    if (skipCount === 0) { // Should be skipped
      passed++;
    } else {
      failed++;
      failures.push("Less complete device details should have been skipped");
    }

    // Test 5: Update with higher version (should always work)
    console.log("✅ Test 05: Update with higher version");
    const higherVersionDevice = {
      uuid: "field-test-001",
      basicUdi: "12345678901234",
      primaryDi: "12345678901234567890",
      deviceName: "Test Device v2",
      versionNumber: "2"
    };

    const higherVersionCount = db.saveDeviceDetails([higherVersionDevice]);
    if (higherVersionCount === 1) {
      passed++;
    } else {
      failed++;
      failures.push("Higher version device details update failed");
    }

    // Test 6: Test with array fields
    console.log("✅ Test 06: Test field completeness with array fields");
    const deviceWithArrays = {
      uuid: "field-test-002",
      basicUdi: "98765432109876",
      primaryDi: "98765432109876543210",
      deviceName: "Array Test Device",
      tradeNames: ["Brand A", "Brand B"],
      marketInfoLinks: [
        { url: "https://example.com", description: "Product page" }
      ],
      additionalDescriptions: ["Description 1", "Description 2"],
      versionNumber: "1"
    };

    const arrayInsertCount = db.saveDeviceDetails([deviceWithArrays]);
    if (arrayInsertCount === 1) {
      passed++;
    } else {
      failed++;
      failures.push("Device with arrays insertion failed");
    }

    // Test 7: Try to update array device with empty arrays (should be skipped)
    console.log("✅ Test 07: Update array device with empty arrays (should skip)");
    const deviceWithEmptyArrays = {
      uuid: "field-test-002",
      basicUdi: "98765432109876",
      primaryDi: "98765432109876543210",
      deviceName: "Array Test Device",
      tradeNames: [], // Empty array
      marketInfoLinks: [], // Empty array
      additionalDescriptions: [], // Empty array
      manufacturer: { name: "New Manufacturer" }, // One new field
      versionNumber: "1"
    };

    const emptyArrayCount = db.saveDeviceDetails([deviceWithEmptyArrays]);
    if (emptyArrayCount === 0) { // Should be skipped
      passed++;
    } else {
      failed++;
      failures.push("Device with empty arrays should have been skipped");
    }

    console.log();
    console.log("=".repeat(75));
    console.log(`📊 Field Completeness Integration Test Results: ${passed} passed, ${failed} failed`);

    if (failed === 0) {
      console.log("🎉 All field completeness integration tests passed!");
      console.log();
      console.log("✅ Field Completeness Test Coverage:");
      console.log("   - versionNumber field in device_details table");
      console.log("   - Field completeness comparison with complex objects");
      console.log("   - JSON field handling in completeness calculation");
      console.log("   - Array field handling (populated vs empty)");
      console.log("   - Version precedence over field completeness");
      return true;
    } else {
      console.log("❌ Field completeness test failures detected:");
      failures.forEach(failure => {
        console.log(`   ${failure}`);
      });
      return false;
    }

  } catch (error) {
    console.error('Error during field completeness tests:', error);
    return false;
  } finally {
    // Cleanup
    if (db) {
      db.close();
    }
    if (existsSync(TEST_DB_PATH)) {
      unlinkSync(TEST_DB_PATH);
    }
  }
}

// Execute tests if this file is run directly
if (process.argv[1] === __filename) {
  runFieldCompletenessTests().then(success => {
    process.exit(success ? 0 : 1);
  }).catch(error => {
    console.error('Error running field completeness tests:', error);
    process.exit(1);
  });
}

// Export for use in other test files
export { runFieldCompletenessTests };
