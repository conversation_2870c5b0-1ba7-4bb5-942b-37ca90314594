#!/usr/bin/env node

/**
 * Test Runner for EUDAMED CLI Data Processing Tool
 *
 * This script discovers and runs all test files in the tests directory.
 * Supports running specific test categories (unit, integration) and
 * provides comprehensive reporting.
 *
 * Usage:
 *   node tests/run-tests.js                 # Run all tests
 *   node tests/run-tests.js --unit          # Run unit tests only
 *   node tests/run-tests.js --integration   # Run integration tests only
 *   node tests/run-tests.js --verbose       # Verbose output
 *
 * <AUTHOR> Data Processing Team
 * @since 2024-01-XX
 */

import { readdir, stat } from 'fs/promises';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';
import { spawn } from 'child_process';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

/**
 * Configuration for test runner
 */
const config = {
  testDir: __dirname,
  unitTestDir: join(__dirname, 'unit'),
  integrationTestDir: join(__dirname, 'integration'),
  testFilePattern: /\.test\.js$/,
  integrationTestPattern: /\.integration\.test\.js$/,
  verbose: false
};

/**
 * Parse command line arguments
 */
function parseArgs() {
  const args = process.argv.slice(2);
  const options = {
    runUnit: true,
    runIntegration: true,
    verbose: false
  };

  if (args.includes('--unit')) {
    options.runIntegration = false;
  }

  if (args.includes('--integration')) {
    options.runUnit = false;
  }

  if (args.includes('--verbose')) {
    options.verbose = true;
    config.verbose = true;
  }

  return options;
}

/**
 * Discover test files in a directory
 */
async function discoverTests(directory, pattern = config.testFilePattern) {
  const testFiles = [];

  try {
    const files = await readdir(directory);

    for (const file of files) {
      const filePath = join(directory, file);
      const stats = await stat(filePath);

      if (stats.isFile() && pattern.test(file)) {
        testFiles.push(filePath);
      }
    }
  } catch (error) {
    if (error.code !== 'ENOENT') {
      console.warn(`Warning: Could not read directory ${directory}: ${error.message}`);
    }
  }

  return testFiles;
}

/**
 * Run a single test file
 */
async function runTestFile(testFile) {
  return new Promise((resolve) => {
    const child = spawn('node', [testFile], {
      stdio: config.verbose ? 'inherit' : 'pipe'
    });

    let stdout = '';
    let stderr = '';

    if (!config.verbose) {
      child.stdout?.on('data', (data) => {
        stdout += data.toString();
      });

      child.stderr?.on('data', (data) => {
        stderr += data.toString();
      });
    }

    child.on('close', (code) => {
      resolve({
        file: testFile,
        success: code === 0,
        stdout,
        stderr,
        exitCode: code
      });
    });
  });
}

/**
 * Main test runner function
 */
async function runTests() {
  const options = parseArgs();
  const startTime = Date.now();

  console.log('🧪 EUDAMED CLI Test Runner');
  console.log('='.repeat(50));
  console.log();

  const testSuites = [];

  // Discover unit tests
  if (options.runUnit) {
    const unitTests = await discoverTests(config.unitTestDir);
    if (unitTests.length > 0) {
      testSuites.push({
        name: 'Unit Tests',
        files: unitTests
      });
    }
  }

  // Discover integration tests
  if (options.runIntegration) {
    const integrationTests = await discoverTests(config.integrationTestDir, config.integrationTestPattern);
    if (integrationTests.length > 0) {
      testSuites.push({
        name: 'Integration Tests',
        files: integrationTests
      });
    }
  }

  if (testSuites.length === 0) {
    console.log('⚠️  No test files found.');
    console.log();
    console.log('Expected test locations:');
    console.log(`   Unit tests: ${config.unitTestDir}`);
    console.log(`   Integration tests: ${config.integrationTestDir}`);
    return false;
  }

  let totalTests = 0;
  let totalPassed = 0;
  let totalFailed = 0;
  const failures = [];

  // Run test suites
  for (const suite of testSuites) {
    console.log(`📁 ${suite.name} (${suite.files.length} files)`);
    console.log('-'.repeat(30));

    for (const testFile of suite.files) {
      const fileName = testFile.split('/').pop() || testFile.split('\\').pop();
      process.stdout.write(`   Running ${fileName}... `);

      const result = await runTestFile(testFile);
      totalTests++;

      if (result.success) {
        console.log('✅ PASS');
        totalPassed++;
      } else {
        console.log('❌ FAIL');
        totalFailed++;
        failures.push(result);

        if (!config.verbose && result.stderr) {
          console.log(`      Error: ${result.stderr.trim()}`);
        }
      }
    }

    console.log();
  }

  // Summary
  const endTime = Date.now();
  const duration = ((endTime - startTime) / 1000).toFixed(2);

  console.log('='.repeat(50));
  console.log(`📊 Test Summary (${duration}s)`);
  console.log(`   Total: ${totalTests}`);
  console.log(`   Passed: ${totalPassed}`);
  console.log(`   Failed: ${totalFailed}`);

  if (totalFailed === 0) {
    console.log();
    console.log('🎉 All tests passed!');
    return true;
  } else {
    console.log();
    console.log('❌ Test failures:');
    failures.forEach(failure => {
      const fileName = failure.file.split('/').pop() || failure.file.split('\\').pop();
      console.log(`   ${fileName} (exit code: ${failure.exitCode})`);
    });
    return false;
  }
}

// Run tests if this file is executed directly
if (process.argv[1] === __filename) {
  runTests()
    .then(success => {
      process.exit(success ? 0 : 1);
    })
    .catch(error => {
      console.error('Test runner error:', error);
      process.exit(1);
    });
}

export { runTests, discoverTests, runTestFile };
