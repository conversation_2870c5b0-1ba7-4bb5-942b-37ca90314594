import { json } from '@sveltejs/kit';
import { fetchEudamedData } from '$lib/eudamedApi'; // Adjust path as needed

export async function GET({ params }) {
  const srn = params.srn;

  const riskCombinations = [
    { legislation: 'refdata.applicable-legislation.mdd', riskClass: 'refdata.risk-class.class-i', risk: 'mdd-i' },
    { legislation: 'refdata.applicable-legislation.mdd', riskClass: 'refdata.risk-class.class-iia', risk: 'mdd-iia' },
    { legislation: 'refdata.applicable-legislation.mdd', riskClass: 'refdata.risk-class.class-iib', risk: 'mdd-iib' },
    { legislation: 'refdata.applicable-legislation.mdd', riskClass: 'refdata.risk-class.class-iii', risk: 'mdd-iii' },
    { legislation: 'refdata.applicable-legislation.mdr', riskClass: 'refdata.risk-class.class-i', risk: 'mdr-i' },
    { legislation: 'refdata.applicable-legislation.mdr', riskClass: 'refdata.risk-class.class-iia', risk: 'mdr-iia' },
    { legislation: 'refdata.applicable-legislation.mdr', riskClass: 'refdata.risk-class.class-iib', risk: 'mdr-iib' },
    { legislation: 'refdata.applicable-legislation.mdr', riskClass: 'refdata.risk-class.class-iii', risk: 'mdr-iii' },
    { legislation: 'refdata.applicable-legislation.aimdd', riskClass: 'refdata.risk-class.aimdd', risk: 'aimdd' },
    { legislation: 'refdata.applicable-legislation.ivdr', riskClass: 'refdata.risk-class.class-a', risk: 'ivdr-a' },
    { legislation: 'refdata.applicable-legislation.ivdr', riskClass: 'refdata.risk-class.class-b', risk: 'ivdr-b' },
    { legislation: 'refdata.applicable-legislation.ivdr', riskClass: 'refdata.risk-class.class-c', risk: 'ivdr-c' },
    { legislation: 'refdata.applicable-legislation.ivdr', riskClass: 'refdata.risk-class.class-d', risk: 'ivdr-d' },
    { legislation: 'refdata.applicable-legislation.ivdd', riskClass: 'refdata.risk-class.ivd-annex-ii-list-a', risk: 'ivdd-a' },
    { legislation: 'refdata.applicable-legislation.ivdd', riskClass: 'refdata.risk-class.ivd-annex-ii-list-b', risk: 'ivdd-b' },
    { legislation: 'refdata.applicable-legislation.ivdd', riskClass: 'refdata.risk-class.ivd-devices-self-testing', risk: 'ivdd-testing' },
    { legislation: 'refdata.applicable-legislation.ivdd', riskClass: 'refdata.risk-class.ivd-general', risk: 'ivdd-general' },
  ];

  const fetchPromises = riskCombinations.map(async ({ legislation, riskClass, risk }) => {
    const { data, error } = await fetchEudamedData('/api/devices/udiDiData', {
      srn: srn,
      applicableLegislation: legislation,
      riskClassCode: riskClass,
      size: '300'
    });

    if (error) {
      console.error(`Error fetching devices for SRN ${srn}, Legislation ${legislation}, Risk Class ${riskClass}:`, error);
      return { risk: null, hasOnMarketDevice: false };
    }

    const hasOnMarketDevice = data && data.content && data.content.some(device => device.deviceStatusType?.code === 'on-the-market');

    return { risk: hasOnMarketDevice ? risk : null, hasOnMarketDevice };
  });

  const results = await Promise.all(fetchPromises);

  const foundRisks = new Set();
  results.forEach(result => {
    if (result.risk) {
      foundRisks.add(result.risk);
    }
  });

  return json({ risks: Array.from(foundRisks) });
}