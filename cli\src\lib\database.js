import initSqlJs from 'sql.js';
import { existsSync, mkdirSync, readFileSync, writeFileSync } from 'fs';
import { dirname } from 'path';

class DeviceDatabase {
  constructor(dbPath) {
    this.dbPath = dbPath;
    this.db = null;
  }

  async initialize() {
    // Ensure directory exists
    const dir = dirname(this.dbPath);
    if (!existsSync(dir)) {
      mkdirSync(dir, { recursive: true });
    }

    // Initialize SQL.js
    const SQL = await initSqlJs();

    // Try to load existing database or create new one
    if (existsSync(this.dbPath)) {
      const filebuffer = readFileSync(this.dbPath);
      this.db = new SQL.Database(filebuffer);
    } else {
      this.db = new SQL.Database();
    }

    // Create devices table with schema matching mapDeviceSummary output
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS devices (
        uuid TEXT PRIMARY KEY,
        basicUdi TEXT,
        udiDi TEXT,
        risk TEXT,
        name TEXT,
        reference TEXT,
        status TEXT,
        versionNumber TEXT,
        manufacturer_srn TEXT,
        manufacturer_name TEXT,
        manufacturer_status TEXT,
        authorisedRepresentative_srn TEXT,
        authorisedRepresentative_name TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Create unified_devices table combining both device summary and device details schemas
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS unified_devices (
        uuid TEXT PRIMARY KEY,

        -- Identification fields (prioritizing device details naming)
        basicUdi TEXT,
        primaryDi TEXT,  -- Replaces udiDi from devices table
        secondaryDi TEXT,
        directMarkingDi TEXT,
        legislation TEXT,
        riskClass TEXT,  -- Replaces risk from devices table
        device BOOLEAN,
        specialDeviceType TEXT,

        -- Manufacturer information (JSON for comprehensive data, separate fields for compatibility)
        manufacturer TEXT,  -- JSON object with full manufacturer data
        manufacturer_srn TEXT,  -- Separate field for backward compatibility
        manufacturer_name TEXT,  -- Separate field for backward compatibility
        manufacturer_status TEXT,  -- Separate field for backward compatibility

        -- Authorised Representative information (JSON for comprehensive data, separate fields for compatibility)
        authorisedRepresentative TEXT,  -- JSON object with full AR data
        authorisedRepresentative_srn TEXT,  -- Separate field for backward compatibility
        authorisedRepresentative_name TEXT,  -- Separate field for backward compatibility

        -- Device names and descriptions
        deviceName TEXT,  -- Replaces name from devices table
        tradeNames TEXT, -- JSON array
        reference TEXT,
        placedOnTheMarket TEXT,
        marketInfoLinks TEXT, -- JSON array
        additionalDescriptions TEXT, -- JSON array
        additionalInformationUrl TEXT,

        -- Boolean characteristics
        reprocessed BOOLEAN,
        baseQuantity INTEGER,
        reusable BOOLEAN,
        singleUse BOOLEAN,
        maxNumberOfReuses INTEGER,
        active BOOLEAN,
        administeringMedicine BOOLEAN,
        animalTissues BOOLEAN,
        annexXVIApplicable BOOLEAN,
        companionDiagnostics BOOLEAN,
        endocrineDisruptor BOOLEAN,
        humanProduct BOOLEAN,
        humanTissues BOOLEAN,
        implantable BOOLEAN,
        instrument BOOLEAN,
        kit BOOLEAN,
        latex BOOLEAN,
        measuringFunction BOOLEAN,
        medicinalProduct BOOLEAN,
        microbialSubstances BOOLEAN,
        nearPatientTesting BOOLEAN,
        oemApplicable BOOLEAN,
        professionalTesting BOOLEAN,
        reagent BOOLEAN,
        selfTesting BOOLEAN,
        sterile BOOLEAN,
        sterilization BOOLEAN,
        typeExaminationApplicable BOOLEAN,

        -- Linked information (JSON arrays/objects)
        multiComponent BOOLEAN,
        componentDis TEXT, -- JSON array
        clinicalInvestigationLinks TEXT, -- JSON array
        clinicalSizes TEXT, -- JSON array
        cmrSubstances TEXT, -- JSON array
        criticalWarnings TEXT, -- JSON array
        deviceCertificateInfoList TEXT, -- JSON array
        deviceCertificateInfoListForDisplay TEXT, -- JSON array
        linkedSscp TEXT, -- JSON object

        -- Status and versioning
        status TEXT,  -- Device status from summary data
        versionDate DATETIME,
        versionState TEXT,
        discardedDate DATETIME,
        versionNumber TEXT,
        deviceStatus TEXT, -- JSON object
        notifiedBodyDecision TEXT, -- JSON object
        newDevice BOOLEAN,

        -- Data source tracking
        data_source TEXT DEFAULT 'summary',  -- 'summary', 'details', or 'merged'

        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Create device_details table with comprehensive schema for mapCombinedDeviceData output
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS device_details (
        uuid TEXT PRIMARY KEY,
        basicUdi TEXT,
        primaryDi TEXT,
        secondaryDi TEXT,
        directMarkingDi TEXT,
        legislation TEXT,
        riskClass TEXT,
        device BOOLEAN,
        specialDeviceType TEXT,

        -- Manufacturer information (JSON)
        manufacturer TEXT,

        -- Authorised Representative information (JSON)
        authorisedRepresentative TEXT,

        -- Device names and descriptions
        deviceName TEXT,
        tradeNames TEXT, -- JSON array
        reference TEXT,
        placedOnTheMarket TEXT,
        marketInfoLinks TEXT, -- JSON array
        additionalDescriptions TEXT, -- JSON array
        additionalInformationUrl TEXT,

        -- Boolean flags
        reprocessed BOOLEAN,
        baseQuantity INTEGER,
        reusable BOOLEAN,
        singleUse BOOLEAN,
        maxNumberOfReuses INTEGER,
        active BOOLEAN,
        administeringMedicine BOOLEAN,
        animalTissues BOOLEAN,
        annexXVIApplicable BOOLEAN,
        companionDiagnostics BOOLEAN,
        endocrineDisruptor BOOLEAN,
        humanProduct BOOLEAN,
        humanTissues BOOLEAN,
        implantable BOOLEAN,
        instrument BOOLEAN,
        kit BOOLEAN,
        latex BOOLEAN,
        measuringFunction BOOLEAN,
        medicinalProduct BOOLEAN,
        microbialSubstances BOOLEAN,
        nearPatientTesting BOOLEAN,
        oemApplicable BOOLEAN,
        professionalTesting BOOLEAN,
        reagent BOOLEAN,
        selfTesting BOOLEAN,
        sterile BOOLEAN,
        sterilization BOOLEAN,
        typeExaminationApplicable BOOLEAN,

        -- Linked information (JSON arrays/objects)
        multiComponent BOOLEAN,
        componentDis TEXT, -- JSON array
        clinicalInvestigationLinks TEXT, -- JSON array
        clinicalSizes TEXT, -- JSON array
        cmrSubstances TEXT, -- JSON array
        criticalWarnings TEXT, -- JSON array
        deviceCertificateInfoList TEXT, -- JSON array
        deviceCertificateInfoListForDisplay TEXT, -- JSON array
        linkedSscp TEXT, -- JSON object

        -- Status and versioning
        versionDate DATETIME,
        versionState TEXT,
        discardedDate DATETIME,
        versionNumber TEXT,
        deviceStatus TEXT, -- JSON object
        notifiedBodyDecision TEXT, -- JSON object
        newDevice BOOLEAN,

        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Create indexes for efficient queries
    this.db.exec(`
      -- Legacy table indexes
      CREATE INDEX IF NOT EXISTS idx_devices_udiDi ON devices(udiDi);
      CREATE INDEX IF NOT EXISTS idx_devices_basicUdi ON devices(basicUdi);
      CREATE INDEX IF NOT EXISTS idx_devices_manufacturer_srn ON devices(manufacturer_srn);
      CREATE INDEX IF NOT EXISTS idx_device_details_primaryDi ON device_details(primaryDi);
      CREATE INDEX IF NOT EXISTS idx_device_details_basicUdi ON device_details(basicUdi);
      CREATE INDEX IF NOT EXISTS idx_device_details_legislation ON device_details(legislation);
      CREATE INDEX IF NOT EXISTS idx_device_details_riskClass ON device_details(riskClass);

      -- Unified table indexes
      CREATE INDEX IF NOT EXISTS idx_unified_devices_primaryDi ON unified_devices(primaryDi);
      CREATE INDEX IF NOT EXISTS idx_unified_devices_basicUdi ON unified_devices(basicUdi);
      CREATE INDEX IF NOT EXISTS idx_unified_devices_manufacturer_srn ON unified_devices(manufacturer_srn);
      CREATE INDEX IF NOT EXISTS idx_unified_devices_legislation ON unified_devices(legislation);
      CREATE INDEX IF NOT EXISTS idx_unified_devices_riskClass ON unified_devices(riskClass);
      CREATE INDEX IF NOT EXISTS idx_unified_devices_data_source ON unified_devices(data_source);
      CREATE INDEX IF NOT EXISTS idx_unified_devices_versionNumber ON unified_devices(versionNumber);
    `);

    console.log(`Database initialized at: ${this.dbPath}`);
  }

  saveDevices(devices) {
    if (!this.db) {
      throw new Error('Database not initialized. Call initialize() first.');
    }

    let savedCount = 0;

    // Prepare the insert statement for legacy devices table
    const insertSql = `
      INSERT OR REPLACE INTO devices (
        uuid, basicUdi, udiDi, risk, name, reference, status, versionNumber,
        manufacturer_srn, manufacturer_name, manufacturer_status,
        authorisedRepresentative_srn, authorisedRepresentative_name, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'))
    `;

    for (const device of devices) {
      try {
        this.db.run(insertSql, [
          device.uuid || null,
          device.basicUdi || null,
          device.udiDi || null,
          device.risk || null,
          device.name || null,
          device.reference || null,
          device.status || null,
          device.versionNumber || null,
          device.manufacturer?.srn || null,
          device.manufacturer?.name || null,
          device.manufacturer?.status || null,
          device.authorisedRepresentative?.srn || null,
          device.authorisedRepresentative?.name || null
        ]);
        savedCount++;
      } catch (error) {
        console.error('Error saving device:', device.uuid || device.udiDi, error.message);
      }
    }

    // Also save to unified table
    const unifiedSavedCount = this.saveUnifiedDevices(devices, 'summary');

    // Save database to file after batch insert
    try {
      const data = this.db.export();
      writeFileSync(this.dbPath, data);
    } catch (error) {
      console.error('Error saving database to file:', error);
    }

    console.log(`Legacy devices saved: ${savedCount}, Unified devices saved: ${unifiedSavedCount}`);
    return savedCount;
  }

  saveDeviceDetails(deviceDetails) {
    if (!this.db) {
      throw new Error('Database not initialized. Call initialize() first.');
    }

    let savedCount = 0;

    // Prepare the insert statement for device details
    const insertSql = `
      INSERT OR REPLACE INTO device_details (
        uuid, basicUdi, primaryDi, secondaryDi, directMarkingDi, legislation, riskClass,
        device, specialDeviceType, manufacturer, authorisedRepresentative,
        deviceName, tradeNames, reference, placedOnTheMarket, marketInfoLinks,
        additionalDescriptions, additionalInformationUrl, reprocessed, baseQuantity,
        reusable, singleUse, maxNumberOfReuses, active, administeringMedicine,
        animalTissues, annexXVIApplicable, companionDiagnostics, endocrineDisruptor,
        humanTissues, implantable, instrument, kit, latex, measuringFunction,
        medicinalProduct, microbialSubstances, nearPatientTesting, oemApplicable,
        professionalTesting, reagent, selfTesting, sterile, sterilization,
        typeExaminationApplicable, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'))
    `;

    for (const device of deviceDetails) {
      try {
        this.db.run(insertSql, [
          device.uuid || null,
          device.basicUdi || null,
          device.primaryDi || null,
          device.secondaryDi || null,
          device.directMarkingDi || null,
          device.legislation || null,
          device.riskClass || null,
          device.device || null,
          device.specialDeviceType || null,
          device.manufacturer ? JSON.stringify(device.manufacturer) : null,
          device.authorisedRepresentative ? JSON.stringify(device.authorisedRepresentative) : null,
          device.deviceName || null,
          device.tradeNames ? JSON.stringify(device.tradeNames) : null,
          device.reference || null,
          device.placedOnTheMarket || null,
          device.marketInfoLinks ? JSON.stringify(device.marketInfoLinks) : null,
          device.additionalDescriptions ? JSON.stringify(device.additionalDescriptions) : null,
          device.additionalInformationUrl || null,
          device.reprocessed || null,
          device.baseQuantity || null,
          device.reusable || null,
          device.singleUse || null,
          device.maxNumberOfReuses || null,
          device.active || null,
          device.administeringMedicine || null,
          device.animalTissues || null,
          device.annexXVIApplicable || null,
          device.companionDiagnostics || null,
          device.endocrineDisruptor || null,
          device.humanTissues || null,
          device.implantable || null,
          device.instrument || null,
          device.kit || null,
          device.latex || null,
          device.measuringFunction || null,
          device.medicinalProduct || null,
          device.microbialSubstances || null,
          device.nearPatientTesting || null,
          device.oemApplicable || null,
          device.professionalTesting || null,
          device.reagent || null,
          device.selfTesting || null,
          device.sterile || null,
          device.sterilization || null,
          device.typeExaminationApplicable || null
        ]);
        savedCount++;
      } catch (error) {
        console.error('Error saving device details:', device.uuid || device.primaryDi, error.message);
      }
    }

    // Also save to unified table
    const unifiedSavedCount = this.saveUnifiedDevices(deviceDetails, 'details');

    // Save database to file after batch insert
    try {
      const data = this.db.export();
      writeFileSync(this.dbPath, data);
    } catch (error) {
      console.error('Error saving database to file:', error);
    }

    console.log(`Legacy device details saved: ${savedCount}, Unified devices saved: ${unifiedSavedCount}`);
    return savedCount;
  }

  /**
   * Save devices to the unified_devices table with version-based conflict resolution
   * @param {Array} devices - Array of device objects (from mapDeviceSummary or mapCombinedDeviceData)
   * @param {string} dataSource - Source of the data ('summary', 'details', or 'merged')
   * @returns {number} - Number of devices saved
   */
  saveUnifiedDevices(devices, dataSource = 'summary') {
    if (!this.db) {
      throw new Error('Database not initialized. Call initialize() first.');
    }

    let savedCount = 0;
    let skippedCount = 0;

    // Prepare statements for version checking and insertion
    const checkVersionSql = `SELECT versionNumber, * FROM unified_devices WHERE uuid = ?`;
    const insertSql = `
      INSERT OR REPLACE INTO unified_devices (
        uuid, basicUdi, primaryDi, secondaryDi, directMarkingDi, legislation, riskClass,
        device, specialDeviceType, manufacturer, manufacturer_srn, manufacturer_name, manufacturer_status,
        authorisedRepresentative, authorisedRepresentative_srn, authorisedRepresentative_name,
        deviceName, tradeNames, reference, placedOnTheMarket, marketInfoLinks, additionalDescriptions,
        additionalInformationUrl, reprocessed, baseQuantity, reusable, singleUse, maxNumberOfReuses,
        active, administeringMedicine, animalTissues, annexXVIApplicable, companionDiagnostics,
        endocrineDisruptor, humanProduct, humanTissues, implantable, instrument, kit, latex,
        measuringFunction, medicinalProduct, microbialSubstances, nearPatientTesting, oemApplicable,
        professionalTesting, reagent, selfTesting, sterile, sterilization, typeExaminationApplicable,
        multiComponent, componentDis, clinicalInvestigationLinks, clinicalSizes, cmrSubstances,
        criticalWarnings, deviceCertificateInfoList, deviceCertificateInfoListForDisplay, linkedSscp,
        status, versionDate, versionState, discardedDate, versionNumber, deviceStatus,
        notifiedBodyDecision, newDevice, data_source, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'))
    `;

    for (const device of devices) {
      try {
        const deviceId = device.uuid;
        if (!deviceId) {
          console.warn('Skipping device without UUID:', device.primaryDi || device.udiDi || 'unknown');
          skippedCount++;
          continue;
        }

        // Check existing version if record exists
        let shouldUpdate = true;
        let existingRecord = null;
        const existingResult = this.db.exec(checkVersionSql, [deviceId]);

        if (existingResult.length > 0 && existingResult[0].values.length > 0) {
          const row = existingResult[0].values[0];
          const columns = existingResult[0].columns;

          // Convert row array to object
          existingRecord = {};
          columns.forEach((col, index) => {
            existingRecord[col] = row[index];
          });

          const existingVersion = existingRecord.versionNumber;
          const updateDecision = this.shouldUpdateRecord(device.versionNumber, existingVersion, device, existingRecord);
          shouldUpdate = updateDecision.shouldUpdate;

          if (!shouldUpdate) {
            console.log(`Skipping device ${deviceId} - ${updateDecision.reason}`);
            skippedCount++;
            continue;
          }
        }

        // Proceed with insert/update
        this.db.run(insertSql, [
          device.uuid || null,
          device.basicUdi || null,
          device.primaryDi || device.udiDi || null,  // Handle both naming conventions
          device.secondaryDi || null,
          device.directMarkingDi || null,
          device.legislation || null,
          device.riskClass || device.risk || null,  // Handle both naming conventions
          device.device || null,
          device.specialDeviceType || null,
          device.manufacturer ? JSON.stringify(device.manufacturer) : null,
          device.manufacturer?.srn || null,
          device.manufacturer?.name || null,
          device.manufacturer?.status || null,
          device.authorisedRepresentative ? JSON.stringify(device.authorisedRepresentative) : null,
          device.authorisedRepresentative?.srn || null,
          device.authorisedRepresentative?.name || null,
          device.deviceName || device.name || null,  // Handle both naming conventions
          device.tradeNames ? JSON.stringify(device.tradeNames) : null,
          device.reference || null,
          device.placedOnTheMarket || null,
          device.marketInfoLinks ? JSON.stringify(device.marketInfoLinks) : null,
          device.additionalDescriptions ? JSON.stringify(device.additionalDescriptions) : null,
          device.additionalInformationUrl || null,
          device.reprocessed || null,
          device.baseQuantity || null,
          device.reusable || null,
          device.singleUse || null,
          device.maxNumberOfReuses || null,
          device.active || null,
          device.administeringMedicine || null,
          device.animalTissues || null,
          device.annexXVIApplicable || null,
          device.companionDiagnostics || null,
          device.endocrineDisruptor || null,
          device.humanProduct || null,
          device.humanTissues || null,
          device.implantable || null,
          device.instrument || null,
          device.kit || null,
          device.latex || null,
          device.measuringFunction || null,
          device.medicinalProduct || null,
          device.microbialSubstances || null,
          device.nearPatientTesting || null,
          device.oemApplicable || null,
          device.professionalTesting || null,
          device.reagent || null,
          device.selfTesting || null,
          device.sterile || null,
          device.sterilization || null,
          device.typeExaminationApplicable || null,
          device.multiComponent || null,
          device.componentDis ? JSON.stringify(device.componentDis) : null,
          device.clinicalInvestigationLinks ? JSON.stringify(device.clinicalInvestigationLinks) : null,
          device.clinicalSizes ? JSON.stringify(device.clinicalSizes) : null,
          device.cmrSubstances ? JSON.stringify(device.cmrSubstances) : null,
          device.criticalWarnings ? JSON.stringify(device.criticalWarnings) : null,
          device.deviceCertificateInfoList ? JSON.stringify(device.deviceCertificateInfoList) : null,
          device.deviceCertificateInfoListForDisplay ? JSON.stringify(device.deviceCertificateInfoListForDisplay) : null,
          device.linkedSscp ? JSON.stringify(device.linkedSscp) : null,
          device.status || null,
          device.versionDate || null,
          device.versionState || null,
          device.discardedDate || null,
          device.versionNumber || null,
          device.deviceStatus ? JSON.stringify(device.deviceStatus) : null,
          device.notifiedBodyDecision ? JSON.stringify(device.notifiedBodyDecision) : null,
          device.newDevice || null,
          dataSource
        ]);
        savedCount++;
      } catch (error) {
        console.error('Error saving unified device:', device.uuid || device.primaryDi || device.udiDi, error.message);
        skippedCount++;
      }
    }

    // Save database to file after batch insert
    try {
      const data = this.db.export();
      writeFileSync(this.dbPath, data);
    } catch (error) {
      console.error('Error saving database to file:', error);
    }

    console.log(`Unified devices saved: ${savedCount}, skipped: ${skippedCount}`);
    return savedCount;
  }

  getDeviceCount() {
    if (!this.db) {
      throw new Error('Database not initialized. Call initialize() first.');
    }

    const result = this.db.exec('SELECT COUNT(*) as count FROM devices');
    if (result.length > 0 && result[0].values.length > 0) {
      return result[0].values[0][0];
    }
    return 0;
  }

  getDeviceDetailsCount() {
    if (!this.db) {
      throw new Error('Database not initialized. Call initialize() first.');
    }

    const result = this.db.exec('SELECT COUNT(*) as count FROM device_details');
    if (result.length > 0 && result[0].values.length > 0) {
      return result[0].values[0][0];
    }
    return 0;
  }

  /**
   * Count populated fields in a record for field completeness comparison
   * @param {Object} record - The record to analyze
   * @returns {number} - Number of non-null, non-empty fields
   */
  countPopulatedFields(record) {
    if (!record || typeof record !== 'object') return 0;

    let count = 0;
    for (const [key, value] of Object.entries(record)) {
      if (value !== null && value !== undefined && value !== '') {
        if (typeof value === 'object') {
          // For nested objects, count if they have any populated fields
          if (Array.isArray(value)) {
            if (value.length > 0) count++;
          } else {
            const nestedCount = this.countPopulatedFields(value);
            if (nestedCount > 0) count++;
          }
        } else {
          count++;
        }
      }
    }
    return count;
  }

  /**
   * Compare version numbers for conflict resolution with field completeness fallback
   * @param {string|number|null} incomingVersion - Version number from incoming record
   * @param {string|number|null} existingVersion - Version number from existing database record
   * @param {Object|null} incomingRecord - Full incoming record for field completeness comparison
   * @param {Object|null} existingRecord - Full existing record for field completeness comparison
   * @returns {Object} - {shouldUpdate: boolean, reason: string}
   */
  shouldUpdateRecord(incomingVersion, existingVersion, incomingRecord = null, existingRecord = null) {
    // Convert to numbers for comparison, treating null/undefined as 0
    const incoming = incomingVersion ? parseInt(incomingVersion, 10) : 0;
    const existing = existingVersion ? parseInt(existingVersion, 10) : 0;

    // Handle invalid version numbers
    if (isNaN(incoming)) return { shouldUpdate: false, reason: 'Invalid incoming version number' };
    if (isNaN(existing)) return { shouldUpdate: true, reason: 'Invalid existing version number' };

    // Version-based comparison
    if (incoming > existing) {
      return { shouldUpdate: true, reason: `Higher version (${incoming} > ${existing})` };
    }
    if (incoming < existing) {
      return { shouldUpdate: false, reason: `Lower version (${incoming} < ${existing})` };
    }

    // Equal versions - check field completeness if records provided
    if (incoming === existing && incomingRecord && existingRecord) {
      const incomingFieldCount = this.countPopulatedFields(incomingRecord);
      const existingFieldCount = this.countPopulatedFields(existingRecord);

      if (incomingFieldCount > existingFieldCount) {
        return {
          shouldUpdate: true,
          reason: `Equal version but more complete (${incomingFieldCount} vs ${existingFieldCount} fields)`
        };
      }
      return {
        shouldUpdate: false,
        reason: `Equal version and equal/less complete (${incomingFieldCount} vs ${existingFieldCount} fields)`
      };
    }

    // Equal versions without field comparison - skip by default
    return { shouldUpdate: false, reason: `Equal version (${incoming})` };
  }

  getUnifiedDeviceCount() {
    if (!this.db) {
      throw new Error('Database not initialized. Call initialize() first.');
    }

    const result = this.db.exec('SELECT COUNT(*) as count FROM unified_devices');
    if (result.length > 0 && result[0].values.length > 0) {
      return result[0].values[0][0];
    }
    return 0;
  }

  getUnifiedDeviceCountBySource(dataSource = null) {
    if (!this.db) {
      throw new Error('Database not initialized. Call initialize() first.');
    }

    let sql = 'SELECT data_source, COUNT(*) as count FROM unified_devices';
    let params = [];

    if (dataSource) {
      sql += ' WHERE data_source = ?';
      params.push(dataSource);
    } else {
      sql += ' GROUP BY data_source';
    }

    const result = this.db.exec(sql, params);
    if (result.length > 0 && result[0].values.length > 0) {
      if (dataSource) {
        return result[0].values[0][1]; // Return count for specific source
      } else {
        // Return object with counts by source
        const counts = {};
        result[0].values.forEach(row => {
          counts[row[0]] = row[1];
        });
        return counts;
      }
    }
    return dataSource ? 0 : {};
  }

  close() {
    if (this.db) {
      this.db.close();
      this.db = null;
    }
  }
}

export { DeviceDatabase };
