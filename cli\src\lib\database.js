import initSqlJs from 'sql.js';
import { existsSync, mkdirSync, readFileSync, writeFileSync } from 'fs';
import { dirname } from 'path';

class DeviceDatabase {
  constructor(dbPath) {
    this.dbPath = dbPath;
    this.db = null;
  }

  async initialize() {
    // Ensure directory exists
    const dir = dirname(this.dbPath);
    if (!existsSync(dir)) {
      mkdirSync(dir, { recursive: true });
    }

    // Initialize SQL.js
    const SQL = await initSqlJs();

    // Try to load existing database or create new one
    if (existsSync(this.dbPath)) {
      const filebuffer = readFileSync(this.dbPath);
      this.db = new SQL.Database(filebuffer);
    } else {
      this.db = new SQL.Database();
    }

    // Create devices table with schema matching mapDeviceSummary output
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS devices (
        uuid TEXT PRIMARY KEY,
        basicUdi TEXT,
        udiDi TEXT,
        risk TEXT,
        name TEXT,
        reference TEXT,
        status TEXT,
        versionNumber TEXT,
        manufacturer_srn TEXT,
        manufacturer_name TEXT,
        manufacturer_status TEXT,
        authorisedRepresentative_srn TEXT,
        authorisedRepresentative_name TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Create unified_devices table combining both device summary and device details schemas
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS unified_devices (
        uuid TEXT PRIMARY KEY,

        -- Identification fields (prioritizing device details naming)
        basicUdi TEXT,
        primaryDi TEXT,
        secondaryDi TEXT,
        directMarkingDi TEXT,
        legislation TEXT,
        riskClass TEXT,
        device BOOLEAN,
        specialDeviceType TEXT,

        -- Manufacturer information (JSON for comprehensive data, separate fields for compatibility)
        manufacturer TEXT,
        manufacturer_srn TEXT,
        manufacturer_name TEXT,
        manufacturer_status TEXT,

        -- Authorised Representative information (JSON for comprehensive data, separate fields for compatibility)
        authorisedRepresentative TEXT,
        authorisedRepresentative_srn TEXT,
        authorisedRepresentative_name TEXT,

        -- Device names and descriptions
        deviceName TEXT,
        tradeNames TEXT,
        reference TEXT,
        placedOnTheMarket TEXT,
        marketInfoLinks TEXT,
        additionalDescriptions TEXT,
        additionalInformationUrl TEXT,

        -- Boolean characteristics
        reprocessed BOOLEAN,
        baseQuantity INTEGER,
        reusable BOOLEAN,
        singleUse BOOLEAN,
        maxNumberOfReuses INTEGER,
        active BOOLEAN,
        administeringMedicine BOOLEAN,
        animalTissues BOOLEAN,
        annexXVIApplicable BOOLEAN,
        companionDiagnostics BOOLEAN,
        endocrineDisruptor BOOLEAN,
        humanProduct BOOLEAN,
        humanTissues BOOLEAN,
        implantable BOOLEAN,
        instrument BOOLEAN,
        kit BOOLEAN,
        latex BOOLEAN,
        measuringFunction BOOLEAN,
        medicinalProduct BOOLEAN,
        microbialSubstances BOOLEAN,
        nearPatientTesting BOOLEAN,
        oemApplicable BOOLEAN,
        professionalTesting BOOLEAN,
        reagent BOOLEAN,
        selfTesting BOOLEAN,
        sterile BOOLEAN,
        sterilization BOOLEAN,
        typeExaminationApplicable BOOLEAN,

        -- Linked information (JSON arrays/objects)
        multiComponent BOOLEAN,
        componentDis TEXT,
        clinicalInvestigationLinks TEXT,
        clinicalSizes TEXT,
        cmrSubstances TEXT,
        criticalWarnings TEXT,
        deviceCertificateInfoList TEXT,
        deviceCertificateInfoListForDisplay TEXT,
        linkedSscp TEXT,

        -- Status and versioning
        status TEXT,
        versionDate DATETIME,
        versionState TEXT,
        discardedDate DATETIME,
        versionNumber TEXT,
        deviceStatus TEXT,
        notifiedBodyDecision TEXT,
        newDevice BOOLEAN,

        -- Data source tracking
        data_source TEXT DEFAULT 'summary',

        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Create device_details table with comprehensive schema for mapCombinedDeviceData output
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS device_details (
        uuid TEXT PRIMARY KEY,
        basicUdi TEXT,
        primaryDi TEXT,
        secondaryDi TEXT,
        directMarkingDi TEXT,
        legislation TEXT,
        riskClass TEXT,
        device BOOLEAN,
        specialDeviceType TEXT,

        -- Manufacturer information (JSON)
        manufacturer TEXT,

        -- Authorised Representative information (JSON)
        authorisedRepresentative TEXT,

        -- Device names and descriptions
        deviceName TEXT,
        tradeNames TEXT, -- JSON array
        reference TEXT,
        placedOnTheMarket TEXT,
        marketInfoLinks TEXT, -- JSON array
        additionalDescriptions TEXT, -- JSON array
        additionalInformationUrl TEXT,

        -- Boolean flags
        reprocessed BOOLEAN,
        baseQuantity INTEGER,
        reusable BOOLEAN,
        singleUse BOOLEAN,
        maxNumberOfReuses INTEGER,
        active BOOLEAN,
        administeringMedicine BOOLEAN,
        animalTissues BOOLEAN,
        annexXVIApplicable BOOLEAN,
        companionDiagnostics BOOLEAN,
        endocrineDisruptor BOOLEAN,
        humanTissues BOOLEAN,
        implantable BOOLEAN,
        instrument BOOLEAN,
        kit BOOLEAN,
        latex BOOLEAN,
        measuringFunction BOOLEAN,
        medicinalProduct BOOLEAN,
        microbialSubstances BOOLEAN,
        nearPatientTesting BOOLEAN,
        oemApplicable BOOLEAN,
        professionalTesting BOOLEAN,
        reagent BOOLEAN,
        selfTesting BOOLEAN,
        sterile BOOLEAN,
        sterilization BOOLEAN,
        typeExaminationApplicable BOOLEAN,

        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Create indexes for efficient queries
    this.db.exec(`
      -- Legacy table indexes
      CREATE INDEX IF NOT EXISTS idx_devices_udiDi ON devices(udiDi);
      CREATE INDEX IF NOT EXISTS idx_devices_basicUdi ON devices(basicUdi);
      CREATE INDEX IF NOT EXISTS idx_devices_manufacturer_srn ON devices(manufacturer_srn);
      CREATE INDEX IF NOT EXISTS idx_device_details_primaryDi ON device_details(primaryDi);
      CREATE INDEX IF NOT EXISTS idx_device_details_basicUdi ON device_details(basicUdi);
      CREATE INDEX IF NOT EXISTS idx_device_details_legislation ON device_details(legislation);
      CREATE INDEX IF NOT EXISTS idx_device_details_riskClass ON device_details(riskClass);

      -- Unified table indexes
      CREATE INDEX IF NOT EXISTS idx_unified_devices_primaryDi ON unified_devices(primaryDi);
      CREATE INDEX IF NOT EXISTS idx_unified_devices_basicUdi ON unified_devices(basicUdi);
      CREATE INDEX IF NOT EXISTS idx_unified_devices_manufacturer_srn ON unified_devices(manufacturer_srn);
      CREATE INDEX IF NOT EXISTS idx_unified_devices_legislation ON unified_devices(legislation);
      CREATE INDEX IF NOT EXISTS idx_unified_devices_riskClass ON unified_devices(riskClass);
      CREATE INDEX IF NOT EXISTS idx_unified_devices_data_source ON unified_devices(data_source);
      CREATE INDEX IF NOT EXISTS idx_unified_devices_versionNumber ON unified_devices(versionNumber);
    `);

    console.log(`Database initialized at: ${this.dbPath}`);
  }

  saveDevices(devices) {
    if (!this.db) {
      throw new Error('Database not initialized. Call initialize() first.');
    }

    let savedCount = 0;

    // Prepare the insert statement for legacy devices table
    const insertSql = `
      INSERT OR REPLACE INTO devices (
        uuid, basicUdi, udiDi, risk, name, reference, status, versionNumber,
        manufacturer_srn, manufacturer_name, manufacturer_status,
        authorisedRepresentative_srn, authorisedRepresentative_name, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'))
    `;

    for (const device of devices) {
      try {
        this.db.run(insertSql, [
          device.uuid || null,
          device.basicUdi || null,
          device.udiDi || null,
          device.risk || null,
          device.name || null,
          device.reference || null,
          device.status || null,
          device.versionNumber || null,
          device.manufacturer?.srn || null,
          device.manufacturer?.name || null,
          device.manufacturer?.status || null,
          device.authorisedRepresentative?.srn || null,
          device.authorisedRepresentative?.name || null
        ]);
        savedCount++;
      } catch (error) {
        console.error('Error saving device:', device.uuid || device.udiDi, error.message);
      }
    }

    // Also save to unified table
    const unifiedSavedCount = this.saveUnifiedDevices(devices, 'summary');

    // Save database to file after batch insert
    try {
      const data = this.db.export();
      writeFileSync(this.dbPath, data);
    } catch (error) {
      console.error('Error saving database to file:', error);
    }

    console.log(`Legacy devices saved: ${savedCount}, Unified devices saved: ${unifiedSavedCount}`);
    return savedCount;
  }

  saveDeviceDetails(deviceDetails) {
    if (!this.db) {
      throw new Error('Database not initialized. Call initialize() first.');
    }

    let savedCount = 0;

    // Prepare the insert statement for device details
    const insertSql = `
      INSERT OR REPLACE INTO device_details (
        uuid, basicUdi, primaryDi, secondaryDi, directMarkingDi, legislation, riskClass,
        device, specialDeviceType, manufacturer, authorisedRepresentative,
        deviceName, tradeNames, reference, placedOnTheMarket, marketInfoLinks,
        additionalDescriptions, additionalInformationUrl, reprocessed, baseQuantity,
        reusable, singleUse, maxNumberOfReuses, active, administeringMedicine,
        animalTissues, annexXVIApplicable, companionDiagnostics, endocrineDisruptor,
        humanTissues, implantable, instrument, kit, latex, measuringFunction,
        medicinalProduct, microbialSubstances, nearPatientTesting, oemApplicable,
        professionalTesting, reagent, selfTesting, sterile, sterilization,
        typeExaminationApplicable, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'))
    `;

    for (const device of deviceDetails) {
      try {
        this.db.run(insertSql, [
          device.uuid || null,
          device.basicUdi || null,
          device.primaryDi || null,
          device.secondaryDi || null,
          device.directMarkingDi || null,
          device.legislation || null,
          device.riskClass || null,
          device.device || null,
          device.specialDeviceType || null,
          device.manufacturer ? JSON.stringify(device.manufacturer) : null,
          device.authorisedRepresentative ? JSON.stringify(device.authorisedRepresentative) : null,
          device.deviceName || null,
          device.tradeNames ? JSON.stringify(device.tradeNames) : null,
          device.reference || null,
          device.placedOnTheMarket || null,
          device.marketInfoLinks ? JSON.stringify(device.marketInfoLinks) : null,
          device.additionalDescriptions ? JSON.stringify(device.additionalDescriptions) : null,
          device.additionalInformationUrl || null,
          device.reprocessed || null,
          device.baseQuantity || null,
          device.reusable || null,
          device.singleUse || null,
          device.maxNumberOfReuses || null,
          device.active || null,
          device.administeringMedicine || null,
          device.animalTissues || null,
          device.annexXVIApplicable || null,
          device.companionDiagnostics || null,
          device.endocrineDisruptor || null,
          device.humanTissues || null,
          device.implantable || null,
          device.instrument || null,
          device.kit || null,
          device.latex || null,
          device.measuringFunction || null,
          device.medicinalProduct || null,
          device.microbialSubstances || null,
          device.nearPatientTesting || null,
          device.oemApplicable || null,
          device.professionalTesting || null,
          device.reagent || null,
          device.selfTesting || null,
          device.sterile || null,
          device.sterilization || null,
          device.typeExaminationApplicable || null
        ]);
        savedCount++;
      } catch (error) {
        console.error('Error saving device details:', device.uuid || device.primaryDi, error.message);
      }
    }

    // Also save to unified table
    const unifiedSavedCount = this.saveUnifiedDevices(deviceDetails, 'details');

    // Save database to file after batch insert
    try {
      const data = this.db.export();
      writeFileSync(this.dbPath, data);
    } catch (error) {
      console.error('Error saving database to file:', error);
    }

    console.log(`Legacy device details saved: ${savedCount}, Unified devices saved: ${unifiedSavedCount}`);
    return savedCount;
  }

  /**
   * Save devices to the unified_devices table with version-based conflict resolution
   * @param {Array} devices - Array of device objects (from mapDeviceSummary or mapCombinedDeviceData)
   * @param {string} dataSource - Source of the data ('summary', 'details', or 'merged')
   * @returns {number} - Number of devices saved
   */
  saveUnifiedDevices(devices, dataSource = 'summary') {
    if (!this.db) {
      throw new Error('Database not initialized. Call initialize() first.');
    }

    let savedCount = 0;
    let skippedCount = 0;

    // Insert statement for unified_devices table (69 columns, excluding created_at and updated_at which have DEFAULT values)
    const insertSql = `
      INSERT OR REPLACE INTO unified_devices (
        uuid, basicUdi, primaryDi, secondaryDi, directMarkingDi, legislation, riskClass,
        device, specialDeviceType, manufacturer, manufacturer_srn, manufacturer_name, manufacturer_status,
        authorisedRepresentative, authorisedRepresentative_srn, authorisedRepresentative_name,
        deviceName, tradeNames, reference, placedOnTheMarket, marketInfoLinks, additionalDescriptions,
        additionalInformationUrl, reprocessed, baseQuantity, reusable, singleUse, maxNumberOfReuses,
        active, administeringMedicine, animalTissues, annexXVIApplicable, companionDiagnostics,
        endocrineDisruptor, humanProduct, humanTissues, implantable, instrument, kit, latex,
        measuringFunction, medicinalProduct, microbialSubstances, nearPatientTesting, oemApplicable,
        professionalTesting, reagent, selfTesting, sterile, sterilization, typeExaminationApplicable,
        multiComponent, componentDis, clinicalInvestigationLinks, clinicalSizes, cmrSubstances,
        criticalWarnings, deviceCertificateInfoList, deviceCertificateInfoListForDisplay, linkedSscp,
        status, versionDate, versionState, discardedDate, versionNumber, deviceStatus,
        notifiedBodyDecision, newDevice, data_source
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    for (const device of devices) {
      try {
        const deviceId = device.uuid;
        if (!deviceId) {
          console.warn('Skipping device without UUID:', device.primaryDi || device.udiDi || 'unknown');
          skippedCount++;
          continue;
        }

        // Proceed with insert/update (69 values for 69 columns)
        this.db.run(insertSql, [
          device.uuid || null,                                                          // 1
          device.basicUdi || null,                                                      // 2
          device.primaryDi || device.udiDi || null,                                     // 3
          device.secondaryDi || null,                                                   // 4
          device.directMarkingDi || null,                                               // 5
          device.legislation || null,                                                   // 6
          device.riskClass || device.risk || null,                                      // 7
          device.device || null,                                                        // 8
          device.specialDeviceType || null,                                             // 9
          device.manufacturer ? JSON.stringify(device.manufacturer) : null,            // 10
          device.manufacturer?.srn || null,                                             // 11
          device.manufacturer?.name || null,                                            // 12
          device.manufacturer?.status || null,                                          // 13
          device.authorisedRepresentative ? JSON.stringify(device.authorisedRepresentative) : null, // 14
          device.authorisedRepresentative?.srn || null,                                 // 15
          device.authorisedRepresentative?.name || null,                                // 16
          device.deviceName || device.name || null,                                     // 17
          device.tradeNames ? JSON.stringify(device.tradeNames) : null,                 // 18
          device.reference || null,                                                     // 19
          device.placedOnTheMarket || null,                                             // 20
          device.marketInfoLinks ? JSON.stringify(device.marketInfoLinks) : null,      // 21
          device.additionalDescriptions ? JSON.stringify(device.additionalDescriptions) : null, // 22
          device.additionalInformationUrl || null,                                      // 23
          device.reprocessed || null,                                                   // 24
          device.baseQuantity || null,                                                  // 25
          device.reusable || null,                                                      // 26
          device.singleUse || null,                                                     // 27
          device.maxNumberOfReuses || null,                                             // 28
          device.active || null,                                                        // 29
          device.administeringMedicine || null,                                         // 30
          device.animalTissues || null,                                                 // 31
          device.annexXVIApplicable || null,                                            // 32
          device.companionDiagnostics || null,                                          // 33
          device.endocrineDisruptor || null,                                            // 34
          device.humanProduct || null,                                                  // 35
          device.humanTissues || null,                                                  // 36
          device.implantable || null,                                                   // 37
          device.instrument || null,                                                    // 38
          device.kit || null,                                                           // 39
          device.latex || null,                                                         // 40
          device.measuringFunction || null,                                             // 41
          device.medicinalProduct || null,                                              // 42
          device.microbialSubstances || null,                                           // 43
          device.nearPatientTesting || null,                                            // 44
          device.oemApplicable || null,                                                 // 45
          device.professionalTesting || null,                                           // 46
          device.reagent || null,                                                       // 47
          device.selfTesting || null,                                                   // 48
          device.sterile || null,                                                       // 49
          device.sterilization || null,                                                 // 50
          device.typeExaminationApplicable || null,                                     // 51
          device.multiComponent || null,                                                // 52
          device.componentDis ? JSON.stringify(device.componentDis) : null,             // 53
          device.clinicalInvestigationLinks ? JSON.stringify(device.clinicalInvestigationLinks) : null, // 54
          device.clinicalSizes ? JSON.stringify(device.clinicalSizes) : null,          // 55
          device.cmrSubstances ? JSON.stringify(device.cmrSubstances) : null,          // 56
          device.criticalWarnings ? JSON.stringify(device.criticalWarnings) : null,    // 57
          device.deviceCertificateInfoList ? JSON.stringify(device.deviceCertificateInfoList) : null, // 58
          device.deviceCertificateInfoListForDisplay ? JSON.stringify(device.deviceCertificateInfoListForDisplay) : null, // 59
          device.linkedSscp ? JSON.stringify(device.linkedSscp) : null,                // 60
          device.status || null,                                                        // 61
          device.versionDate || null,                                                   // 62
          device.versionState || null,                                                  // 63
          device.discardedDate || null,                                                 // 64
          device.versionNumber || null,                                                 // 65
          device.deviceStatus ? JSON.stringify(device.deviceStatus) : null,            // 66
          device.notifiedBodyDecision ? JSON.stringify(device.notifiedBodyDecision) : null, // 67
          device.newDevice || null,                                                     // 68
          dataSource                                                                    // 69
        ]);
        savedCount++;
      } catch (error) {
        console.error('Error saving unified device:', device.uuid || device.primaryDi || device.udiDi, error.message);
        skippedCount++;
      }
    }

    // Save database to file after batch insert
    try {
      const data = this.db.export();
      writeFileSync(this.dbPath, data);
    } catch (error) {
      console.error('Error saving database to file:', error);
    }

    console.log(`Unified devices saved: ${savedCount}, skipped: ${skippedCount}`);
    return savedCount;
  }

  getDeviceCount() {
    if (!this.db) {
      throw new Error('Database not initialized. Call initialize() first.');
    }

    const result = this.db.exec('SELECT COUNT(*) as count FROM devices');
    if (result.length > 0 && result[0].values.length > 0) {
      return result[0].values[0][0];
    }
    return 0;
  }

  getDeviceDetailsCount() {
    if (!this.db) {
      throw new Error('Database not initialized. Call initialize() first.');
    }

    const result = this.db.exec('SELECT COUNT(*) as count FROM device_details');
    if (result.length > 0 && result[0].values.length > 0) {
      return result[0].values[0][0];
    }
    return 0;
  }

  getUnifiedDeviceCount() {
    if (!this.db) {
      throw new Error('Database not initialized. Call initialize() first.');
    }

    const result = this.db.exec('SELECT COUNT(*) as count FROM unified_devices');
    if (result.length > 0 && result[0].values.length > 0) {
      return result[0].values[0][0];
    }
    return 0;
  }

  getUnifiedDeviceCountBySource(dataSource = null) {
    if (!this.db) {
      throw new Error('Database not initialized. Call initialize() first.');
    }

    let sql = 'SELECT data_source, COUNT(*) as count FROM unified_devices';
    let params = [];

    if (dataSource) {
      sql += ' WHERE data_source = ?';
      params.push(dataSource);
    } else {
      sql += ' GROUP BY data_source';
    }

    const result = this.db.exec(sql, params);
    if (result.length > 0 && result[0].values.length > 0) {
      if (dataSource) {
        return result[0].values[0][1]; // Return count for specific source
      } else {
        // Return object with counts by source
        const counts = {};
        result[0].values.forEach(row => {
          counts[row[0]] = row[1];
        });
        return counts;
      }
    }
    return dataSource ? 0 : {};
  }

  close() {
    if (this.db) {
      this.db.close();
      this.db = null;
    }
  }
}

export { DeviceDatabase };
