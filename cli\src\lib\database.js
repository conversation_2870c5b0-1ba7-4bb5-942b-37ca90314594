import initSqlJs from 'sql.js';
import { existsSync, mkdirSync, readFileSync, writeFileSync } from 'fs';
import { dirname } from 'path';

class DeviceDatabase {
  constructor(dbPath) {
    this.dbPath = dbPath;
    this.db = null;
  }

  async initialize() {
    // Ensure directory exists
    const dir = dirname(this.dbPath);
    if (!existsSync(dir)) {
      mkdirSync(dir, { recursive: true });
    }

    // Initialize SQL.js
    const SQL = await initSqlJs();

    // Try to load existing database or create new one
    if (existsSync(this.dbPath)) {
      const filebuffer = readFileSync(this.dbPath);
      this.db = new SQL.Database(filebuffer);
    } else {
      this.db = new SQL.Database();
    }

    // Create devices table with schema matching mapDeviceSummary output
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS devices (
        uuid TEXT PRIMARY KEY,
        basicUdi TEXT,
        udiDi TEXT,
        risk TEXT,
        name TEXT,
        reference TEXT,
        status TEXT,
        versionNumber TEXT,
        manufacturer_srn TEXT,
        manufacturer_name TEXT,
        manufacturer_status TEXT,
        authorisedRepresentative_srn TEXT,
        authorisedRepresentative_name TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Create device_details table with comprehensive schema for mapCombinedDeviceData output
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS device_details (
        uuid TEXT PRIMARY KEY,
        basicUdi TEXT,
        primaryDi TEXT,
        secondaryDi TEXT,
        directMarkingDi TEXT,
        legislation TEXT,
        riskClass TEXT,
        device BOOLEAN,
        specialDeviceType TEXT,

        -- Manufacturer information (JSON)
        manufacturer TEXT,

        -- Authorised Representative information (JSON)
        authorisedRepresentative TEXT,

        -- Device names and descriptions
        deviceName TEXT,
        tradeNames TEXT, -- JSON array
        reference TEXT,
        placedOnTheMarket TEXT,
        marketInfoLinks TEXT, -- JSON array
        additionalDescriptions TEXT, -- JSON array
        additionalInformationUrl TEXT,

        -- Boolean flags
        reprocessed BOOLEAN,
        baseQuantity INTEGER,
        reusable BOOLEAN,
        singleUse BOOLEAN,
        maxNumberOfReuses INTEGER,
        active BOOLEAN,
        administeringMedicine BOOLEAN,
        animalTissues BOOLEAN,
        annexXVIApplicable BOOLEAN,
        companionDiagnostics BOOLEAN,
        endocrineDisruptor BOOLEAN,
        humanTissues BOOLEAN,
        implantable BOOLEAN,
        instrument BOOLEAN,
        kit BOOLEAN,
        latex BOOLEAN,
        measuringFunction BOOLEAN,
        medicinalProduct BOOLEAN,
        microbialSubstances BOOLEAN,
        nearPatientTesting BOOLEAN,
        oemApplicable BOOLEAN,
        professionalTesting BOOLEAN,
        reagent BOOLEAN,
        selfTesting BOOLEAN,
        sterile BOOLEAN,
        sterilization BOOLEAN,
        typeExaminationApplicable BOOLEAN,

        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Create errors table for comprehensive error logging
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS errors (
        uuid TEXT PRIMARY KEY,
        resource TEXT NOT NULL CHECK (resource IN (
          'deviceSummary', 'deviceDetails', 'deviceUDIDI', 'deviceBasicUDI',
          'actorSummary', 'actor', 'certificate'
        )),
        error_type TEXT NOT NULL CHECK (error_type IN (
          'API_ERROR', 'NETWORK_ERROR', 'PARSING_ERROR', 'DATABASE_ERROR', 'VALIDATION_ERROR'
        )),
        error_message TEXT,
        page INTEGER,
        device_uuid TEXT,
        http_status INTEGER,
        timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
        context TEXT
      )
    `);

    // Create api_request_errors table for tracking failed API requests for retry functionality
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS api_request_errors (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        resource TEXT NOT NULL CHECK (resource IN (
          'deviceSummary', 'deviceUDIDI', 'deviceBasicUDI'
        )),
        uuid TEXT,
        range_param TEXT,
        page_size INTEGER,
        error_message TEXT,
        http_status INTEGER,
        retry_count INTEGER DEFAULT 0,
        last_retry_at DATETIME,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        context TEXT
      )
    `);

    // Create indexes for efficient queries
    this.db.exec(`
      CREATE INDEX IF NOT EXISTS idx_devices_udiDi ON devices(udiDi);
      CREATE INDEX IF NOT EXISTS idx_devices_basicUdi ON devices(basicUdi);
      CREATE INDEX IF NOT EXISTS idx_devices_manufacturer_srn ON devices(manufacturer_srn);
      CREATE INDEX IF NOT EXISTS idx_device_details_primaryDi ON device_details(primaryDi);
      CREATE INDEX IF NOT EXISTS idx_device_details_basicUdi ON device_details(basicUdi);
      CREATE INDEX IF NOT EXISTS idx_device_details_legislation ON device_details(legislation);
      CREATE INDEX IF NOT EXISTS idx_device_details_riskClass ON device_details(riskClass);
      CREATE INDEX IF NOT EXISTS idx_errors_resource ON errors(resource);
      CREATE INDEX IF NOT EXISTS idx_errors_error_type ON errors(error_type);
      CREATE INDEX IF NOT EXISTS idx_errors_timestamp ON errors(timestamp);
      CREATE INDEX IF NOT EXISTS idx_errors_device_uuid ON errors(device_uuid);
      CREATE INDEX IF NOT EXISTS idx_api_request_errors_resource ON api_request_errors(resource);
      CREATE INDEX IF NOT EXISTS idx_api_request_errors_uuid ON api_request_errors(uuid);
      CREATE INDEX IF NOT EXISTS idx_api_request_errors_retry_count ON api_request_errors(retry_count);
      CREATE INDEX IF NOT EXISTS idx_api_request_errors_created_at ON api_request_errors(created_at);
    `);

    console.log(`Database initialized at: ${this.dbPath}`);
  }

  saveDevices(devices) {
    if (!this.db) {
      throw new Error('Database not initialized. Call initialize() first.');
    }

    let savedCount = 0;

    // Prepare the insert statement
    const insertSql = `
      INSERT OR REPLACE INTO devices (
        uuid, basicUdi, udiDi, risk, name, reference, status, versionNumber,
        manufacturer_srn, manufacturer_name, manufacturer_status,
        authorisedRepresentative_srn, authorisedRepresentative_name, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'))
    `;

    for (const device of devices) {
      try {
        this.db.run(insertSql, [
          device.uuid || null,
          device.basicUdi || null,
          device.udiDi || null,
          device.risk || null,
          device.name || null,
          device.reference || null,
          device.status || null,
          device.versionNumber || null,
          device.manufacturer?.srn || null,
          device.manufacturer?.name || null,
          device.manufacturer?.status || null,
          device.authorisedRepresentative?.srn || null,
          device.authorisedRepresentative?.name || null
        ]);
        savedCount++;
      } catch (error) {
        console.error('Error saving device:', device.uuid || device.udiDi, error.message);
      }
    }

    // Save database to file after batch insert
    try {
      const data = this.db.export();
      writeFileSync(this.dbPath, data);
    } catch (error) {
      console.error('Error saving database to file:', error);
    }

    return savedCount;
  }

  saveDeviceDetails(deviceDetails) {
    if (!this.db) {
      throw new Error('Database not initialized. Call initialize() first.');
    }

    let savedCount = 0;

    // Prepare the insert statement for device details
    const insertSql = `
      INSERT OR REPLACE INTO device_details (
        uuid, basicUdi, primaryDi, secondaryDi, directMarkingDi, legislation, riskClass,
        device, specialDeviceType, manufacturer, authorisedRepresentative,
        deviceName, tradeNames, reference, placedOnTheMarket, marketInfoLinks,
        additionalDescriptions, additionalInformationUrl, reprocessed, baseQuantity,
        reusable, singleUse, maxNumberOfReuses, active, administeringMedicine,
        animalTissues, annexXVIApplicable, companionDiagnostics, endocrineDisruptor,
        humanTissues, implantable, instrument, kit, latex, measuringFunction,
        medicinalProduct, microbialSubstances, nearPatientTesting, oemApplicable,
        professionalTesting, reagent, selfTesting, sterile, sterilization,
        typeExaminationApplicable, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'))
    `;

    for (const device of deviceDetails) {
      try {
        this.db.run(insertSql, [
          device.uuid || null,
          device.basicUdi || null,
          device.primaryDi || null,
          device.secondaryDi || null,
          device.directMarkingDi || null,
          device.legislation || null,
          device.riskClass || null,
          device.device || null,
          device.specialDeviceType || null,
          device.manufacturer ? JSON.stringify(device.manufacturer) : null,
          device.authorisedRepresentative ? JSON.stringify(device.authorisedRepresentative) : null,
          device.deviceName || null,
          device.tradeNames ? JSON.stringify(device.tradeNames) : null,
          device.reference || null,
          device.placedOnTheMarket || null,
          device.marketInfoLinks ? JSON.stringify(device.marketInfoLinks) : null,
          device.additionalDescriptions ? JSON.stringify(device.additionalDescriptions) : null,
          device.additionalInformationUrl || null,
          device.reprocessed || null,
          device.baseQuantity || null,
          device.reusable || null,
          device.singleUse || null,
          device.maxNumberOfReuses || null,
          device.active || null,
          device.administeringMedicine || null,
          device.animalTissues || null,
          device.annexXVIApplicable || null,
          device.companionDiagnostics || null,
          device.endocrineDisruptor || null,
          device.humanTissues || null,
          device.implantable || null,
          device.instrument || null,
          device.kit || null,
          device.latex || null,
          device.measuringFunction || null,
          device.medicinalProduct || null,
          device.microbialSubstances || null,
          device.nearPatientTesting || null,
          device.oemApplicable || null,
          device.professionalTesting || null,
          device.reagent || null,
          device.selfTesting || null,
          device.sterile || null,
          device.sterilization || null,
          device.typeExaminationApplicable || null
        ]);
        savedCount++;
      } catch (error) {
        console.error('Error saving device details:', device.uuid || device.primaryDi, error.message);
      }
    }

    // Save database to file after batch insert
    try {
      const data = this.db.export();
      writeFileSync(this.dbPath, data);
    } catch (error) {
      console.error('Error saving database to file:', error);
    }

    return savedCount;
  }

  saveApiRequestError(errorData) {
    if (!this.db) {
      throw new Error('Database not initialized. Call initialize() first.');
    }

    try {
      const insertSql = `
        INSERT INTO api_request_errors (
          resource, uuid, range_param, page_size, error_message,
          http_status, retry_count, context, updated_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, datetime('now'))
      `;

      this.db.run(insertSql, [
        errorData.resource || null,
        errorData.uuid || null,
        errorData.range || null,
        errorData.pageSize || null,
        errorData.errorMessage || null,
        errorData.httpStatus || null,
        errorData.retryCount || 0,
        errorData.context || null
      ]);

      // Save database to file after insert
      const data = this.db.export();
      writeFileSync(this.dbPath, data);

      return true;
    } catch (error) {
      console.error('Error saving API request error:', error.message);
      return false;
    }
  }

  getApiRequestErrors(resource = null, maxRetries = null) {
    if (!this.db) {
      throw new Error('Database not initialized. Call initialize() first.');
    }

    let sql = 'SELECT * FROM api_request_errors';
    const params = [];

    const conditions = [];
    if (resource) {
      conditions.push('resource = ?');
      params.push(resource);
    }
    if (maxRetries !== null) {
      conditions.push('retry_count <= ?');
      params.push(maxRetries);
    }

    if (conditions.length > 0) {
      sql += ' WHERE ' + conditions.join(' AND ');
    }

    sql += ' ORDER BY created_at ASC';

    try {
      const result = this.db.exec(sql, params);
      if (result.length > 0) {
        const columns = result[0].columns;
        const values = result[0].values;
        return values.map(row => {
          const obj = {};
          columns.forEach((col, index) => {
            obj[col] = row[index];
          });
          return obj;
        });
      }
      return [];
    } catch (error) {
      console.error('Error retrieving API request errors:', error.message);
      return [];
    }
  }

  getDeviceCount() {
    if (!this.db) {
      throw new Error('Database not initialized. Call initialize() first.');
    }

    const result = this.db.exec('SELECT COUNT(*) as count FROM devices');
    if (result.length > 0 && result[0].values.length > 0) {
      return result[0].values[0][0];
    }
    return 0;
  }

  getDeviceDetailsCount() {
    if (!this.db) {
      throw new Error('Database not initialized. Call initialize() first.');
    }

    const result = this.db.exec('SELECT COUNT(*) as count FROM device_details');
    if (result.length > 0 && result[0].values.length > 0) {
      return result[0].values[0][0];
    }
    return 0;
  }

  getApiRequestErrorsCount() {
    if (!this.db) {
      throw new Error('Database not initialized. Call initialize() first.');
    }

    const result = this.db.exec('SELECT COUNT(*) as count FROM api_request_errors');
    if (result.length > 0 && result[0].values.length > 0) {
      return result[0].values[0][0];
    }
    return 0;
  }

  close() {
    if (this.db) {
      this.db.close();
      this.db = null;
    }
  }
}

export { DeviceDatabase };
