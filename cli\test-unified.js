#!/usr/bin/env node

/**
 * Simple test script to verify unified devices functionality
 */

import { DeviceDatabase } from './src/lib/database.js';
import { existsSync, unlinkSync } from 'fs';

const TEST_DB_PATH = './test-unified-simple.db';

async function testUnified() {
  console.log('🧪 Testing Unified Devices Functionality');
  console.log('=========================================\n');

  // Clean up any existing test database
  if (existsSync(TEST_DB_PATH)) {
    unlinkSync(TEST_DB_PATH);
  }

  try {
    // Test 1: Initialize database
    console.log("✅ Test 1: Initialize database with unified table");
    const db = new DeviceDatabase(TEST_DB_PATH);
    await db.initialize();
    console.log("   Database initialized successfully");

    // Test 2: Check if unified table exists
    console.log("✅ Test 2: Check unified table exists");
    const tables = db.db.exec("SELECT name FROM sqlite_master WHERE type='table'");
    const tableNames = tables[0]?.values?.flat() || [];
    console.log("   Tables found:", tableNames);

    if (tableNames.includes('unified_devices')) {
      console.log("   ✓ Unified devices table exists");
    } else {
      console.log("   ✗ Unified devices table missing");
    }

    // Test 3: Test unified device count methods
    console.log("✅ Test 3: Test unified device count methods");
    const initialCount = db.getUnifiedDeviceCount();
    const countsBySource = db.getUnifiedDeviceCountBySource();
    console.log("   Initial unified device count:", initialCount);
    console.log("   Counts by source:", countsBySource);

    // Test 4: Save a simple device to unified table
    console.log("✅ Test 4: Save device to unified table");
    const testDevice = {
      uuid: "test-001",
      basicUdi: "B-12345678901234",
      primaryDi: "12345678901234567890",
      deviceName: "Test Device",
      versionNumber: "1",
      manufacturer: {
        srn: "DE-MF-000012345",
        name: "Test Manufacturer"
      }
    };

    const savedCount = db.saveUnifiedDevices([testDevice], 'summary');
    console.log("   Devices saved:", savedCount);

    if (savedCount === 1) {
      console.log("   ✓ Device saved successfully");
    } else {
      console.log("   ✗ Device save failed");
    }

    // Test 5: Verify device was saved
    console.log("✅ Test 5: Verify device was saved");
    const finalCount = db.getUnifiedDeviceCount();
    const finalCountsBySource = db.getUnifiedDeviceCountBySource();
    console.log("   Final unified device count:", finalCount);
    console.log("   Final counts by source:", finalCountsBySource);

    // Clean up
    db.close();
    if (existsSync(TEST_DB_PATH)) {
      unlinkSync(TEST_DB_PATH);
    }

    console.log('\n🎉 All tests completed successfully!');

  } catch (error) {
    console.error('\n❌ Error during testing:', error.message);
    console.error(error.stack);
    process.exit(1);
  }
}

testUnified().catch(console.error);
