//Merge of the actor data on root level & ActorDataPublicView
{
    "uuid": "string",
    "srn": "string",
    "europeanVatNumber": "string",
    "tradeRegister": "string",
    "eori": "string",
    "actorType": "string", //actorType.code
    "legislation": "string", //legislationLinks.legislation.code
    "name": "string",
    "names": { //names & abbreviated
        "abbreviated": true,
        "language": "string",
        "name": "string"
    },
    "status": {
        "startDate": "2025-04-05T13:14:38.173Z",
        "endDate": "2025-04-05T13:14:38.173Z",
        "versionNumber": 0,
        "versionState": "string", //versionState.code
        "statusFromDate": "2025-04-05",
        "latestVersion": true,
        "lastUpdateDate": "2025-04-05T13:14:38.173Z",
        "code": "string" //status.code
    },
    "address": {
      "countryIso2Code": "string",
      "countryName": "string",
      "geographicalAddress": "string",
      "buildingNumber": "string",
      "streetName": "string",
      "postbox": "string",
      "addressComplement": "string",
      "postalZone": "string",
      "cityName": "string",
      "gps": "string"
    },
    "contact": {
        "firstName": "string",
        "familyName": "string",
        "electronicMail": "<EMAIL>",
        "telephone": "string",
        "website": "string"
    },
    "authorisedRepresentatives": [
        {
        "uuid": "string", //authorisedRepresentativeUuid
        "srn": "string",
        "actorType": "string", //actorType.code
        "name": "string",
        "countryIsoCode": "string",
        "actorStatus": {
            "startDate": "2025-04-05T13:14:38.173Z",
            "endDate": "2025-04-05T13:14:38.173Z",
            "versionNumber": 0,
            "versionState": "string", //versionState.code
            "statusFromDate": "2025-04-05",
            "latestVersion": true,
            "lastUpdateDate": "2025-04-05T13:14:38.173Z",
            "status": "string" //status.code
        },
        "names": [
            { 
            "abbreviated": true,
            "language": "string",
            "name": "string"
            }
        ],
        "address": {
            "countryIso2Code": "string",
            "countryName": "string",
            "geographicalAddress": "string"
        },
        "contact": {
            "firstName": "string",
            "familyName": "string",
            "electronicMail": "<EMAIL>",
            "telephone": "string",
            "website": "string"
        }
        }
    ],
    "importers": [
        {
        "uuid": "string",
        "srn": "string",
        "actorType": "string", //actorType.code
        "name": "string",
        "actorStatus": {
            "startDate": "2025-04-05T13:14:38.173Z",
            "endDate": "2025-04-05T13:14:38.173Z",
            "versionNumber": 0,
            "versionState": "string", //versionState.code
            "statusFromDate": "2025-04-05",
            "latestVersion": true,
            "lastUpdateDate": "2025-04-05T13:14:38.173Z",
            "status": "string" //status.code
        },
        "names": { 
            "abbreviated": true,
            "language": "string",
            "name": "string"
        },
        "address": {
            "countryIso2Code": "string",
            "countryName": "string",
            "geographicalAddress": "string"
        },
        "contact": {
            "firstName": "string",
            "familyName": "string",
            "electronicMail": "<EMAIL>",
            "telephone": "string",
            "website": "string"
        }
        }
    ],
    "nonEuManufacturers": [
        {
        "uuid": "string",
        "srn": "string",
        "actorType": "string", //actorType.code
        "name": "string",
        "countryIsoCode": "string",
        "actorStatus": {
            "startDate": "2025-04-05T13:14:38.173Z",
            "endDate": "2025-04-05T13:14:38.173Z",
            "versionNumber": 0,
            "versionState": "string", //versionState.code
            "statusFromDate": "2025-04-05",
            "latestVersion": true,
            "lastUpdateDate": "2025-04-05T13:14:38.173Z",
            "status": "string" //status.code
        },
        "names": { 
            "abbreviated": true,
            "language": "string",
            "name": "string"
        },
        "address": {
            "countryIso2Code": "string",
            "countryName": "string",
            "geographicalAddress": "string"
        },
        "contact": {
            "firstName": "string",
            "familyName": "string",
            "electronicMail": "<EMAIL>",
            "telephone": "string",
            "website": "string"
        }
        }
    ],
    "mandates": [
      {
        "uuid": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        "mandator": {
            "uuid": "string",
            "srn": "string",
            "actorType": "string", //actorType.code
            "name": "string",
            "countryIsoCode": "string",
            "actorStatus": {
                "startDate": "2025-04-05T13:14:38.173Z",
                "endDate": "2025-04-05T13:14:38.173Z",
                "versionNumber": 0,
                "versionState": "string", //versionState.code
                "statusFromDate": "2025-04-05",
                "latestVersion": true,
                "lastUpdateDate": "2025-04-05T13:14:38.173Z",
                "status": "string" //status.code
            },
            "names": { 
                "abbreviated": true,
                "language": "string",
                "name": "string"
            },
            "address": {
                "countryIso2Code": "string",
                "countryName": "string",
                "geographicalAddress": "string"
            },
            "contact": {
                "firstName": "string",
                "familyName": "string",
                "electronicMail": "<EMAIL>",
                "telephone": "string",
                "website": "string"
            }
        },
        "mandatee": {
            "uuid": "string",
            "srn": "string",
            "actorType": "string", //actorType.code
            "name": "string",
            "countryIsoCode": "string",
            "actorStatus": {
                "startDate": "2025-04-05T13:14:38.173Z",
                "endDate": "2025-04-05T13:14:38.173Z",
                "versionNumber": 0,
                "versionState": "string", //versionState.code
                "statusFromDate": "2025-04-05",
                "latestVersion": true,
                "lastUpdateDate": "2025-04-05T13:14:38.173Z",
                "status": "string" //status.code
            },
            "names": { 
                "abbreviated": true,
                "language": "string",
                "name": "string"
            },
            "address": {
                "countryIso2Code": "string",
                "countryName": "string",
                "geographicalAddress": "string"
            },
            "contact": {
                "firstName": "string",
                "familyName": "string",
                "electronicMail": "<EMAIL>",
                "telephone": "string",
                "website": "string"
            }
        },
        "status": {
            "startDate": "2025-04-05T13:14:38.173Z",
            "endDate": "2025-04-05T13:14:38.173Z",
            "versionNumber": 0,
            "versionState": "string", //versionState.code
            "statusFromDate": "2025-04-05",
            "latestVersion": true,
            "lastUpdateDate": "2025-04-05T13:14:38.173Z",
            "code": "string" //status.code
        }
      }
    ],
    "mandateHistory": [
        {
            "uuid": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
            "mandator": {
                "uuid": "string",
                "srn": "string",
                "actorType": "string", //actorType.code
                "name": "string",
                "countryIsoCode": "string",
                "actorStatus": {
                    "startDate": "2025-04-05T13:14:38.173Z",
                    "endDate": "2025-04-05T13:14:38.173Z",
                    "versionNumber": 0,
                    "versionState": "string", //versionState.code
                    "statusFromDate": "2025-04-05",
                    "latestVersion": true,
                    "lastUpdateDate": "2025-04-05T13:14:38.173Z",
                    "status": "string" //status.code
                },
                "names": { 
                    "abbreviated": true,
                    "language": "string",
                    "name": "string"
                },
                "address": {
                    "countryIso2Code": "string",
                    "countryName": "string",
                    "geographicalAddress": "string"
                },
                "contact": {
                    "firstName": "string",
                    "familyName": "string",
                    "electronicMail": "<EMAIL>",
                    "telephone": "string",
                    "website": "string"
                }
            },
            "mandatee": {
                "uuid": "string",
                "srn": "string",
                "actorType": "string", //actorType.code
                "name": "string",
                "countryIsoCode": "string",
                "actorStatus": {
                    "startDate": "2025-04-05T13:14:38.173Z",
                    "endDate": "2025-04-05T13:14:38.173Z",
                    "versionNumber": 0,
                    "versionState": "string", //versionState.code
                    "statusFromDate": "2025-04-05",
                    "latestVersion": true,
                    "lastUpdateDate": "2025-04-05T13:14:38.173Z",
                    "status": "string" //status.code
                },
                "names": { 
                    "abbreviated": true,
                    "language": "string",
                    "name": "string"
                },
                "address": {
                    "countryIso2Code": "string",
                    "countryName": "string",
                    "geographicalAddress": "string"
                },
                "contact": {
                    "firstName": "string",
                    "familyName": "string",
                    "electronicMail": "<EMAIL>",
                    "telephone": "string",
                    "website": "string"
                }
            },
            "status": {
                "startDate": "2025-04-05T13:14:38.173Z",
                "endDate": "2025-04-05T13:14:38.173Z",
                "versionNumber": 0,
                "versionState": "string", //versionState.code
                "statusFromDate": "2025-04-05",
                "latestVersion": true,
                "lastUpdateDate": "2025-04-05T13:14:38.173Z",
                "code": "string" //status.code
            }
          }
    ],
    "subcontractors": [
      {
        "uuid": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        "contractor": {
            "uuid": "string",
            "srn": "string",
            "actorType": "string", //actorType.code
            "name": "string",
            "countryIsoCode": "string",
            "actorStatus": {
                "startDate": "2025-04-05T13:14:38.173Z",
                "endDate": "2025-04-05T13:14:38.173Z",
                "versionNumber": 0,
                "versionState": "string", //versionState.code
                "statusFromDate": "2025-04-05",
                "latestVersion": true,
                "lastUpdateDate": "2025-04-05T13:14:38.173Z",
                "status": "string" //status.code
            },
            "names": { 
                "abbreviated": true,
                "language": "string",
                "name": "string"
            },
            "address": {
                "countryIso2Code": "string",
                "countryName": "string",
                "geographicalAddress": "string"
            },
            "contact": {
                "firstName": "string",
                "familyName": "string",
                "electronicMail": "<EMAIL>",
                "telephone": "string",
                "website": "string"
            }
        },
        "subcontractor": {
            "uuid": "string",
            "srn": "string",
            "actorType": "string", //actorType.code
            "name": "string",
            "countryIsoCode": "string",
            "actorStatus": {
                "startDate": "2025-04-05T13:14:38.173Z",
                "endDate": "2025-04-05T13:14:38.173Z",
                "versionNumber": 0,
                "versionState": "string", //versionState.code
                "statusFromDate": "2025-04-05",
                "latestVersion": true,
                "lastUpdateDate": "2025-04-05T13:14:38.173Z",
                "status": "string" //status.code
            },
            "names": { 
                "abbreviated": true,
                "language": "string",
                "name": "string"
            },
            "address": {
                "countryIso2Code": "string",
                "countryName": "string",
                "geographicalAddress": "string"
            },
            "contact": {
                "firstName": "string",
                "familyName": "string",
                "electronicMail": "<EMAIL>",
                "telephone": "string",
                "website": "string"
            }
        },
        "status": {
            "startDate": "2025-04-05T13:14:38.173Z",
            "endDate": "2025-04-05T13:14:38.173Z",
            "versionNumber": 0,
            "versionState": "string", //versionState.code
            "statusFromDate": "2025-04-05",
            "latestVersion": true,
            "lastUpdateDate": "2025-04-05T13:14:38.173Z",
            "code": "string" //status.code
        }
      }
    ],
    "subcontractorHistory": [
        {
            "uuid": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
            "contractor": {
                "uuid": "string",
                "srn": "string",
                "actorType": "string", //actorType.code
                "name": "string",
                "countryIsoCode": "string",
                "actorStatus": {
                    "startDate": "2025-04-05T13:14:38.173Z",
                    "endDate": "2025-04-05T13:14:38.173Z",
                    "versionNumber": 0,
                    "versionState": "string", //versionState.code
                    "statusFromDate": "2025-04-05",
                    "latestVersion": true,
                    "lastUpdateDate": "2025-04-05T13:14:38.173Z",
                    "status": "string" //status.code
                },
                "names": { 
                    "abbreviated": true,
                    "language": "string",
                    "name": "string"
                },
                "address": {
                    "countryIso2Code": "string",
                    "countryName": "string",
                    "geographicalAddress": "string"
                },
                "contact": {
                    "firstName": "string",
                    "familyName": "string",
                    "electronicMail": "<EMAIL>",
                    "telephone": "string",
                    "website": "string"
                }
            },
            "subcontractor": {
                "uuid": "string",
                "srn": "string",
                "actorType": "string", //actorType.code
                "name": "string",
                "countryIsoCode": "string",
                "actorStatus": {
                    "startDate": "2025-04-05T13:14:38.173Z",
                    "endDate": "2025-04-05T13:14:38.173Z",
                    "versionNumber": 0,
                    "versionState": "string", //versionState.code
                    "statusFromDate": "2025-04-05",
                    "latestVersion": true,
                    "lastUpdateDate": "2025-04-05T13:14:38.173Z",
                    "status": "string" //status.code
                },
                "names": { 
                    "abbreviated": true,
                    "language": "string",
                    "name": "string"
                },
                "address": {
                    "countryIso2Code": "string",
                    "countryName": "string",
                    "geographicalAddress": "string"
                },
                "contact": {
                    "firstName": "string",
                    "familyName": "string",
                    "electronicMail": "<EMAIL>",
                    "telephone": "string",
                    "website": "string"
                }
            },
            "status": {
                "startDate": "2025-04-05T13:14:38.173Z",
                "endDate": "2025-04-05T13:14:38.173Z",
                "versionNumber": 0,
                "versionState": "string", //versionState.code
                "statusFromDate": "2025-04-05",
                "latestVersion": true,
                "lastUpdateDate": "2025-04-05T13:14:38.173Z",
                "code": "string" //status.code
            }
        }
    ],
    "certificates": [
      {
        "uuid": "3fa85f64-5717-4562-b3fc-2c963f66afa6",
        "certificateNumber": "string",
        "certificateType": "string", //certificateType.code
        "certificateStatus": "string", //certificateStatus.code
        "issueDate": "2025-04-05",
        "expiryDate": "2025-04-05",
        "udiDiDataId": 0,
        "status": {
            "startDate": "2025-04-05T13:14:38.173Z",
            "endDate": "2025-04-05T13:14:38.173Z",
            "versionNumber": 0,
            "versionState": "string", //versionState.code
            "statusFromDate": "2025-04-05",
            "latestVersion": true,
            "lastUpdateDate": "2025-04-05T13:14:38.173Z",
            "code": "string" //status.code
        },
        "description": {
          "texts": [
            {
              "language": "", //language.isoCode
              "text": "string"
            }
          ]
        }
      }
    ],
    "regulatoryComplianceResponsibles": [
        { 
        "position": "string",
        "contact": {
            "firstName": "string",
            "familyName": "string",
            "electronicMail": "<EMAIL>",
            "telephone": "string",
            "website": "string"
        },
        "address": {
            "countryIso2Code": "string",
            "countryName": "string",
            "geographicalAddress": "string",
            "buildingNumber": "string",
            "streetName": "string",
            "postbox": "string",
            "addressComplement": "string",
            "postalZone": "string",
            "cityName": "string",
            "gps": "string"
        }
      }
    ]
  }